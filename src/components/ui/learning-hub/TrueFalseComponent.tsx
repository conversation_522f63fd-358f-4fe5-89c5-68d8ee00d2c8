"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { CheckCircle, X, AlertCircle, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TrueFalseData {
  statement: string;
  correct: boolean;
  explanation: string;
}

interface TrueFalseComponentProps {
  data: TrueFalseData;
  onComplete?: (correct: boolean) => void;
}

export default function TrueFalseComponent({ data, onComplete }: TrueFalseComponentProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [answered, setAnswered] = useState(false);

  const handleAnswerSelect = (answer: boolean) => {
    if (answered) return;
    setSelectedAnswer(answer);
  };

  const handleSubmit = () => {
    if (selectedAnswer === null || answered) return;
    
    setShowResult(true);
    setAnswered(true);
    const isCorrect = selectedAnswer === data.correct;
    onComplete?.(isCorrect);
  };

  const resetQuestion = () => {
    setSelectedAnswer(null);
    setShowResult(false);
    setAnswered(false);
  };

  const isCorrect = selectedAnswer === data.correct;

  return (
    <div className="w-full max-w-xl mx-auto bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
      {/* Statement */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-charcoal mb-4">True or False?</h3>
        <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-primary">
          <p className="text-charcoal leading-relaxed">{data.statement}</p>
        </div>
      </div>

      {/* Answer Options */}
      <div className="mb-6">
        <div className="grid grid-cols-2 gap-4">
          {/* True Option */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className={cn(
              "border-2 rounded-lg p-4 cursor-pointer transition-all text-center",
              answered && "cursor-not-allowed",
              showResult && data.correct 
                ? "border-green-500 bg-green-50 text-green-800"
                : showResult && selectedAnswer === true && !data.correct
                ? "border-red-500 bg-red-50 text-red-800"
                : showResult
                ? "border-gray-200 bg-gray-50 text-grey"
                : selectedAnswer === true
                ? "border-primary bg-primary/10 text-primary"
                : "border-gray-200 hover:border-primary/30 hover:bg-primary/5"
            )}
            onClick={() => handleAnswerSelect(true)}
          >
            <div className="flex items-center justify-center gap-3">
              <div className={cn(
                "w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold",
                showResult && data.correct && "bg-green-500 border-green-500 text-white",
                showResult && selectedAnswer === true && !data.correct && "bg-red-500 border-red-500 text-white",
                !showResult && selectedAnswer === true && "bg-primary border-primary text-white",
                !showResult && selectedAnswer !== true && "border-gray-300"
              )}>
                T
              </div>
              <span className="font-medium">True</span>
              
              {showResult && data.correct && (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              {showResult && selectedAnswer === true && !data.correct && (
                <X className="h-5 w-5 text-red-500" />
              )}
            </div>
          </motion.div>

          {/* False Option */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className={cn(
              "border-2 rounded-lg p-4 cursor-pointer transition-all text-center",
              answered && "cursor-not-allowed",
              showResult && !data.correct 
                ? "border-green-500 bg-green-50 text-green-800"
                : showResult && selectedAnswer === false && data.correct
                ? "border-red-500 bg-red-50 text-red-800"
                : showResult
                ? "border-gray-200 bg-gray-50 text-grey"
                : selectedAnswer === false
                ? "border-primary bg-primary/10 text-primary"
                : "border-gray-200 hover:border-primary/30 hover:bg-primary/5"
            )}
            onClick={() => handleAnswerSelect(false)}
          >
            <div className="flex items-center justify-center gap-3">
              <div className={cn(
                "w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold",
                showResult && !data.correct && "bg-green-500 border-green-500 text-white",
                showResult && selectedAnswer === false && data.correct && "bg-red-500 border-red-500 text-white",
                !showResult && selectedAnswer === false && "bg-primary border-primary text-white",
                !showResult && selectedAnswer !== false && "border-gray-300"
              )}>
                F
              </div>
              <span className="font-medium">False</span>
              
              {showResult && !data.correct && (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              {showResult && selectedAnswer === false && data.correct && (
                <X className="h-5 w-5 text-red-500" />
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Submit Button */}
      {!showResult && (
        <div className="flex justify-center">
          <Button
            onClick={handleSubmit}
            disabled={selectedAnswer === null}
            className="bg-primary hover:bg-primary-deep text-white px-8"
          >
            Submit Answer
          </Button>
        </div>
      )}

      {/* Result and Explanation */}
      {showResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 space-y-4"
        >
          {/* Result Badge */}
          <div className={cn(
            "flex items-center gap-2 p-4 rounded-lg",
            isCorrect 
              ? "bg-green-100 text-green-800 border border-green-200"
              : "bg-red-100 text-red-800 border border-red-200"
          )}>
            {isCorrect ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <AlertCircle className="h-5 w-5" />
            )}
            <span className="font-medium">
              {isCorrect ? "Correct! Well done!" : `Incorrect. The answer is ${data.correct ? 'True' : 'False'}.`}
            </span>
          </div>

          {/* Explanation */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 mb-2">Explanation</h4>
                <p className="text-blue-700 text-sm leading-relaxed">{data.explanation}</p>
              </div>
            </div>
          </div>

          {/* Try Again Button */}
          <div className="flex justify-center">
            <Button
              onClick={resetQuestion}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/5"
            >
              Try Another Question
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
