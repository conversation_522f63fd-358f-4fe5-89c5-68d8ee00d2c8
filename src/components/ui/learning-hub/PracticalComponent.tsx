"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Play, CheckCircle, Lightbulb, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PracticalProps {
  scenario: string;
  steps: string[];
  keyPoints: string[];
  onComplete?: () => void;
}

export default function PracticalComponent({ scenario, steps, keyPoints, onComplete }: PracticalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [showKeyPoints, setShowKeyPoints] = useState(false);

  const handleStepComplete = (stepIndex: number) => {
    if (!completedSteps.includes(stepIndex)) {
      setCompletedSteps([...completedSteps, stepIndex]);
    }
    
    if (stepIndex < steps.length - 1) {
      setCurrentStep(stepIndex + 1);
    } else {
      setShowKeyPoints(true);
    }
  };

  const handleComplete = () => {
    onComplete?.();
  };

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-xl border border-gray-200 p-6">
      {/* Scenario */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Play className="h-5 w-5 text-primary" />
          <h3 className="text-xl font-semibold text-charcoal">Practical Scenario</h3>
        </div>
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-6">
          <p className="text-charcoal leading-relaxed text-lg">{scenario}</p>
        </div>
      </div>

      {/* Steps */}
      <div className="mb-8">
        <h4 className="text-lg font-semibold text-charcoal mb-4">Step-by-Step Walkthrough</h4>
        <div className="space-y-4">
          {steps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = currentStep === index;
            const isUpcoming = index > currentStep;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`relative flex items-start gap-4 p-4 rounded-lg border-2 transition-all ${
                  isCompleted
                    ? "border-green-200 bg-green-50"
                    : isCurrent
                    ? "border-primary bg-primary/5"
                    : isUpcoming
                    ? "border-gray-200 bg-gray-50"
                    : "border-gray-200 bg-white"
                }`}
              >
                {/* Step number/status */}
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                  isCompleted
                    ? "bg-green-500 text-white"
                    : isCurrent
                    ? "bg-primary text-white"
                    : "bg-gray-300 text-gray-600"
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>

                {/* Step content */}
                <div className="flex-1">
                  <p className={`leading-relaxed ${
                    isCompleted ? "text-green-800" : isCurrent ? "text-charcoal" : "text-grey"
                  }`}>
                    {step}
                  </p>
                  
                  {isCurrent && !isCompleted && (
                    <Button
                      onClick={() => handleStepComplete(index)}
                      className="mt-3 bg-primary hover:bg-primary-deep text-white"
                      size="sm"
                    >
                      Complete Step
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Key Points */}
      {showKeyPoints && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center gap-2 mb-4">
            <Lightbulb className="h-5 w-5 text-accent" />
            <h4 className="text-lg font-semibold text-charcoal">Key Takeaways</h4>
          </div>
          <div className="bg-gradient-to-r from-accent/5 to-primary/5 rounded-lg p-6">
            <ul className="space-y-3">
              {keyPoints.map((point, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3"
                >
                  <div className="w-2 h-2 rounded-full bg-accent mt-2 flex-shrink-0"></div>
                  <span className="text-charcoal leading-relaxed">{point}</span>
                </motion.li>
              ))}
            </ul>
          </div>
        </motion.div>
      )}

      {/* Complete button */}
      {showKeyPoints && (
        <div className="flex justify-center">
          <Button
            onClick={handleComplete}
            className="bg-accent hover:bg-accent/90 text-white px-8"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Complete Practical Exercise
          </Button>
        </div>
      )}
    </div>
  );
}
