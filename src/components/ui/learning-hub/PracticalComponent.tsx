"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Lightbulb, BookOpen, CheckCircle, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface PracticalData {
  scenario: string;
  explanation: string;
  keyPoints?: string[];
  actionSteps?: string[];
}

interface PracticalComponentProps {
  data: PracticalData;
  onComplete?: () => void;
}

export default function PracticalComponent({ data, onComplete }: PracticalComponentProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completed, setCompleted] = useState(false);

  const steps = [
    { title: "Scenario", content: data.scenario, icon: BookOpen },
    { title: "Explanation", content: data.explanation, icon: Lightbulb },
  ];

  if (data.keyPoints && data.keyPoints.length > 0) {
    steps.push({ 
      title: "Key Points", 
      content: data.keyPoints.join('\n• '), 
      icon: CheckCircle 
    });
  }

  if (data.actionSteps && data.actionSteps.length > 0) {
    steps.push({ 
      title: "Action Steps", 
      content: data.actionSteps.map((step, index) => `${index + 1}. ${step}`).join('\n'), 
      icon: ArrowRight 
    });
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setCompleted(true);
      onComplete?.();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const resetComponent = () => {
    setCurrentStep(0);
    setCompleted(false);
  };

  if (completed) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-2xl mx-auto bg-gradient-to-br from-green-50 to-blue-50 rounded-xl border border-green-200 p-8 text-center shadow-sm"
      >
        <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <CheckCircle className="h-8 w-8 text-green-500" />
        </div>
        <h3 className="text-xl font-bold text-charcoal mb-2">Great Job!</h3>
        <p className="text-grey mb-6">You've completed this practical example. Ready for more learning?</p>
        <Button
          onClick={resetComponent}
          variant="outline"
          className="border-primary text-primary hover:bg-primary/5"
        >
          Review Again
        </Button>
      </motion.div>
    );
  }

  const currentStepData = steps[currentStep];
  const IconComponent = currentStepData.icon;

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      {/* Progress Bar */}
      <div className="bg-gray-100 h-2">
        <div 
          className="bg-gradient-to-r from-primary to-accent h-full transition-all duration-500 ease-out"
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />
      </div>

      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 to-accent/10 p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="bg-white rounded-lg p-2">
            <IconComponent className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-charcoal">{currentStepData.title}</h3>
            <p className="text-sm text-grey">Step {currentStep + 1} of {steps.length}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="min-h-[200px]"
        >
          {currentStep === 0 && (
            // Scenario
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h4 className="font-medium text-blue-800 mb-3 flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Real-World Scenario
              </h4>
              <p className="text-blue-700 leading-relaxed">{currentStepData.content}</p>
            </div>
          )}

          {currentStep === 1 && (
            // Explanation
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
              <h4 className="font-medium text-amber-800 mb-3 flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                How to Apply This
              </h4>
              <p className="text-amber-700 leading-relaxed whitespace-pre-line">{currentStepData.content}</p>
            </div>
          )}

          {currentStep === 2 && data.keyPoints && (
            // Key Points
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h4 className="font-medium text-green-800 mb-3 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Key Points to Remember
              </h4>
              <ul className="space-y-2 text-green-700">
                {data.keyPoints.map((point, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start gap-2"
                  >
                    <span className="text-green-500 mt-1">•</span>
                    <span>{point}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          )}

          {currentStep === 3 && data.actionSteps && (
            // Action Steps
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
              <h4 className="font-medium text-purple-800 mb-3 flex items-center gap-2">
                <ArrowRight className="h-5 w-5" />
                Action Steps
              </h4>
              <ol className="space-y-3 text-purple-700">
                {data.actionSteps.map((step, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start gap-3"
                  >
                    <span className="bg-purple-200 text-purple-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                      {index + 1}
                    </span>
                    <span>{step}</span>
                  </motion.li>
                ))}
              </ol>
            </div>
          )}
        </motion.div>
      </div>

      {/* Navigation */}
      <div className="bg-gray-50 p-6 flex justify-between items-center">
        <Button
          onClick={handlePrevious}
          disabled={currentStep === 0}
          variant="outline"
          className="border-gray-300"
        >
          Previous
        </Button>

        <div className="flex gap-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={cn(
                "w-2 h-2 rounded-full transition-colors",
                index <= currentStep ? "bg-primary" : "bg-gray-300"
              )}
            />
          ))}
        </div>

        <Button
          onClick={handleNext}
          className="bg-primary hover:bg-primary-deep text-white"
        >
          {currentStep === steps.length - 1 ? "Complete" : "Next"}
        </Button>
      </div>
    </div>
  );
}
