"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { RotateCcw, CheckCir<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";

interface FlashcardProps {
  front: string;
  back: string;
  onComplete?: () => void;
}

export default function FlashcardComponent({ front, back, onComplete }: FlashcardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleComplete = () => {
    setIsCompleted(true);
    onComplete?.();
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="relative h-64 perspective-1000">
        <motion.div
          className="relative w-full h-full cursor-pointer"
          onClick={handleFlip}
          animate={{ rotateY: isFlipped ? 180 : 0 }}
          transition={{ duration: 0.6, type: "spring" }}
          style={{ transformStyle: "preserve-3d" }}
        >
          {/* Front of card */}
          <div 
            className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-primary/10 to-accent/10 rounded-xl border-2 border-primary/20 flex items-center justify-center p-6"
            style={{ backfaceVisibility: "hidden" }}
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold text-charcoal mb-4">Question</h3>
              <p className="text-charcoal text-base leading-relaxed">{front}</p>
              <p className="text-grey text-sm mt-4">Click to reveal answer</p>
            </div>
          </div>

          {/* Back of card */}
          <div 
            className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-accent/10 to-primary/10 rounded-xl border-2 border-accent/20 flex items-center justify-center p-6"
            style={{ backfaceVisibility: "hidden", transform: "rotateY(180deg)" }}
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold text-charcoal mb-4">Answer</h3>
              <p className="text-charcoal text-base leading-relaxed">{back}</p>
              <p className="text-grey text-sm mt-4">Click to flip back</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center gap-4 mt-6">
        <Button
          variant="outline"
          onClick={handleFlip}
          className="flex items-center gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          Flip Card
        </Button>
        
        {isFlipped && !isCompleted && (
          <Button
            onClick={handleComplete}
            className="bg-accent hover:bg-accent/90 text-white flex items-center gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            Got It!
          </Button>
        )}
      </div>

      {isCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mt-4"
        >
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-lg">
            <CheckCircle className="h-4 w-4" />
            Flashcard completed!
          </div>
        </motion.div>
      )}
    </div>
  );
}
