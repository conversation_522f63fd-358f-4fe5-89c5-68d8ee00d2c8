"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { RotateCc<PERSON>, CheckCircle, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FlashcardData {
  front: string;
  back: string;
  category?: string;
}

interface FlashcardComponentProps {
  data: FlashcardData;
  onComplete?: (correct: boolean) => void;
}

export default function FlashcardComponent({ data, onComplete }: FlashcardComponentProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [userAnswer, setUserAnswer] = useState<boolean | null>(null);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleAnswer = (correct: boolean) => {
    setUserAnswer(correct);
    onComplete?.(correct);
  };

  const resetCard = () => {
    setIsFlipped(false);
    setUserAnswer(null);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {data.category && (
        <div className="text-sm text-grey mb-2 text-center">
          <span className="bg-primary/10 text-primary px-2 py-1 rounded-full">
            {data.category}
          </span>
        </div>
      )}
      
      <div className="relative h-64 perspective-1000">
        <motion.div
          className="relative w-full h-full cursor-pointer"
          onClick={handleFlip}
          animate={{ rotateY: isFlipped ? 180 : 0 }}
          transition={{ duration: 0.6, type: "spring" }}
          style={{ transformStyle: "preserve-3d" }}
        >
          {/* Front of card */}
          <div
            className={cn(
              "absolute inset-0 w-full h-full backface-hidden",
              "bg-gradient-to-br from-primary to-accent rounded-xl shadow-lg",
              "flex items-center justify-center p-6 text-white"
            )}
            style={{ backfaceVisibility: "hidden" }}
          >
            <div className="text-center">
              <p className="text-lg font-medium mb-4">{data.front}</p>
              <p className="text-sm opacity-80">Click to reveal answer</p>
            </div>
          </div>

          {/* Back of card */}
          <div
            className={cn(
              "absolute inset-0 w-full h-full backface-hidden",
              "bg-white border-2 border-primary/20 rounded-xl shadow-lg",
              "flex items-center justify-center p-6 text-charcoal"
            )}
            style={{ 
              backfaceVisibility: "hidden",
              transform: "rotateY(180deg)"
            }}
          >
            <div className="text-center">
              <p className="text-lg font-medium mb-4">{data.back}</p>
              
              {!userAnswer && (
                <div className="space-y-3">
                  <p className="text-sm text-grey mb-3">Did you get it right?</p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAnswer(true);
                      }}
                      className="bg-green-500 hover:bg-green-600 text-white"
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Yes
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAnswer(false);
                      }}
                      className="border-red-500 text-red-500 hover:bg-red-50"
                    >
                      <X className="h-4 w-4 mr-1" />
                      No
                    </Button>
                  </div>
                </div>
              )}

              {userAnswer !== null && (
                <div className="space-y-3">
                  <div className={cn(
                    "p-3 rounded-lg",
                    userAnswer 
                      ? "bg-green-100 text-green-800 border border-green-200"
                      : "bg-red-100 text-red-800 border border-red-200"
                  )}>
                    {userAnswer ? "Great job! 🎉" : "Keep practicing! 💪"}
                  </div>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      resetCard();
                    }}
                    className="border-primary text-primary hover:bg-primary/5"
                  >
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Try Again
                  </Button>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
