"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, CheckCircle, XCircle, Target, TrendingUp, AlertTriangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface AssessmentQuestion {
  id: string;
  question: string;
  type: "multiple_choice" | "explanation";
  options?: string[];
  correctAnswer?: string;
}

interface AssessmentResult {
  score: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

interface AssessmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  questions: AssessmentQuestion[];
  onComplete: (results: AssessmentResult) => void;
}

export default function AssessmentModal({ isOpen, onClose, questions, onComplete }: AssessmentModalProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<AssessmentResult | null>(null);

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const currentAnswer = answers[currentQuestion?.id] || "";

  const handleAnswerChange = (answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answer
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      // Calculate results (simplified - in real app this would be done by AI)
      const mockResults: AssessmentResult = {
        score: Math.floor(Math.random() * 40) + 60, // 60-100%
        strengths: ["Data Protection Principles", "GDPR Compliance"],
        weaknesses: ["Data Subject Rights", "Controller Obligations"],
        recommendations: [
          "Focus on understanding data subject rights in detail",
          "Practice with real-world controller obligation scenarios",
          "Review the legal basis for data processing"
        ]
      };
      setResults(mockResults);
      setShowResults(true);
      onComplete(mockResults);
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleClose = () => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    setShowResults(false);
    setResults(null);
    onClose();
  };

  const renderQuestion = () => {
    if (!currentQuestion) return null;

    return (
      <div className="space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-grey">Question {currentQuestionIndex + 1} of {questions.length}</span>
            <span className="text-grey">{Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Question */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-charcoal leading-relaxed">
            {currentQuestion.question}
          </h3>

          {/* Answer Input */}
          {currentQuestion.type === "multiple_choice" && currentQuestion.options ? (
            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <motion.label
                  key={index}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    currentAnswer === option
                      ? "border-primary bg-primary/10"
                      : "border-gray-200 hover:border-primary/50"
                  }`}
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={currentAnswer === option}
                    onChange={(e) => handleAnswerChange(e.target.value)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                    currentAnswer === option
                      ? "border-primary bg-primary"
                      : "border-gray-300"
                  }`}>
                    {currentAnswer === option && (
                      <div className="w-full h-full rounded-full bg-white scale-50" />
                    )}
                  </div>
                  <span className="text-charcoal">{option}</span>
                </motion.label>
              ))}
            </div>
          ) : (
            <Textarea
              placeholder="Type your explanation here..."
              value={currentAnswer}
              onChange={(e) => handleAnswerChange(e.target.value)}
              className="min-h-32 resize-none"
            />
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-4">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
          >
            Previous
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!currentAnswer.trim()}
            className="bg-primary hover:bg-primary-deep text-white"
          >
            {isLastQuestion ? "Complete Assessment" : "Next Question"}
          </Button>
        </div>
      </div>
    );
  };

  const renderResults = () => {
    if (!results) return null;

    const getScoreColor = (score: number) => {
      if (score >= 80) return "text-green-600";
      if (score >= 70) return "text-accent";
      return "text-red-600";
    };

    const getScoreBg = (score: number) => {
      if (score >= 80) return "from-green-400 to-green-500";
      if (score >= 70) return "from-accent to-primary";
      return "from-red-400 to-red-500";
    };

    return (
      <div className="space-y-6">
        {/* Score */}
        <div className="text-center">
          <div className={`text-6xl font-bold mb-2 ${getScoreColor(results.score)}`}>
            {results.score}%
          </div>
          <div className="w-32 h-32 mx-auto relative">
            <div className="absolute inset-0 rounded-full bg-gray-200">
              <div 
                className={`absolute inset-0 rounded-full bg-gradient-to-r ${getScoreBg(results.score)}`}
                style={{
                  background: `conic-gradient(from 0deg, var(--primary) 0%, var(--accent) ${results.score}%, #e5e7eb ${results.score}%)`
                }}
              />
              <div className="absolute inset-2 bg-white rounded-full flex items-center justify-center">
                <Target className="h-8 w-8 text-primary" />
              </div>
            </div>
          </div>
          <p className="text-grey mt-4">
            {results.score >= 80 ? "Excellent work!" : results.score >= 70 ? "Good job!" : "Keep practicing!"}
          </p>
        </div>

        {/* Strengths */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <h4 className="font-semibold text-green-800">Strengths</h4>
          </div>
          <ul className="space-y-1">
            {results.strengths.map((strength, index) => (
              <li key={index} className="text-green-700 text-sm">• {strength}</li>
            ))}
          </ul>
        </div>

        {/* Weaknesses */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h4 className="font-semibold text-red-800">Areas for Improvement</h4>
          </div>
          <ul className="space-y-1">
            {results.weaknesses.map((weakness, index) => (
              <li key={index} className="text-red-700 text-sm">• {weakness}</li>
            ))}
          </ul>
        </div>

        {/* Recommendations */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <h4 className="font-semibold text-blue-800">Recommendations</h4>
          </div>
          <ul className="space-y-1">
            {results.recommendations.map((rec, index) => (
              <li key={index} className="text-blue-700 text-sm">• {rec}</li>
            ))}
          </ul>
        </div>

        {/* Close Button */}
        <div className="flex justify-center pt-4">
          <Button
            onClick={handleClose}
            className="bg-primary hover:bg-primary-deep text-white px-8"
          >
            Continue Learning
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogTitle className="sr-only">
          {showResults ? "Assessment Results" : "Knowledge Assessment"}
        </DialogTitle>
        
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-primary/10 rounded-lg p-2">
              <Target className="h-5 w-5 text-primary" />
            </div>
            <h2 className="text-xl font-semibold text-charcoal">
              {showResults ? "Assessment Results" : "Knowledge Assessment"}
            </h2>
          </div>
          <Button variant="ghost" size="sm" onClick={handleClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {showResults ? (
            <motion.div
              key="results"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
            >
              {renderResults()}
            </motion.div>
          ) : (
            <motion.div
              key="questions"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
            >
              {renderQuestion()}
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
