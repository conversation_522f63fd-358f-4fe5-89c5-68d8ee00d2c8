"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { CheckCircle, X, AlertCircle, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface QuestionData {
  question: string;
  options: string[];
  correct: number;
  explanation: string;
}

interface QuestionComponentProps {
  data: QuestionData;
  onComplete?: (correct: boolean) => void;
}

export default function QuestionComponent({ data, onComplete }: QuestionComponentProps) {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [answered, setAnswered] = useState(false);

  const handleOptionSelect = (index: number) => {
    if (answered) return;
    setSelectedOption(index);
  };

  const handleSubmit = () => {
    if (selectedOption === null || answered) return;
    
    setShowResult(true);
    setAnswered(true);
    const isCorrect = selectedOption === data.correct;
    onComplete?.(isCorrect);
  };

  const resetQuestion = () => {
    setSelectedOption(null);
    setShowResult(false);
    setAnswered(false);
  };

  const isCorrect = selectedOption === data.correct;

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
      {/* Question */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-charcoal mb-4">{data.question}</h3>
        
        {/* Options */}
        <div className="space-y-3">
          {data.options.map((option, index) => {
            const isSelected = selectedOption === index;
            const isCorrectOption = index === data.correct;
            
            let optionStyle = "border-gray-200 hover:border-primary/30 hover:bg-primary/5";
            
            if (showResult) {
              if (isCorrectOption) {
                optionStyle = "border-green-500 bg-green-50 text-green-800";
              } else if (isSelected && !isCorrectOption) {
                optionStyle = "border-red-500 bg-red-50 text-red-800";
              } else {
                optionStyle = "border-gray-200 bg-gray-50 text-grey";
              }
            } else if (isSelected) {
              optionStyle = "border-primary bg-primary/10 text-primary";
            }

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "border-2 rounded-lg p-4 cursor-pointer transition-all",
                  optionStyle,
                  answered && "cursor-not-allowed"
                )}
                onClick={() => handleOptionSelect(index)}
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-medium",
                    showResult && isCorrectOption && "bg-green-500 border-green-500 text-white",
                    showResult && isSelected && !isCorrectOption && "bg-red-500 border-red-500 text-white",
                    !showResult && isSelected && "bg-primary border-primary text-white",
                    !showResult && !isSelected && "border-gray-300"
                  )}>
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span className="flex-1">{option}</span>
                  
                  {showResult && isCorrectOption && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                  {showResult && isSelected && !isCorrectOption && (
                    <X className="h-5 w-5 text-red-500" />
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Submit Button */}
      {!showResult && (
        <div className="flex justify-center">
          <Button
            onClick={handleSubmit}
            disabled={selectedOption === null}
            className="bg-primary hover:bg-primary-deep text-white px-8"
          >
            Submit Answer
          </Button>
        </div>
      )}

      {/* Result and Explanation */}
      {showResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 space-y-4"
        >
          {/* Result Badge */}
          <div className={cn(
            "flex items-center gap-2 p-4 rounded-lg",
            isCorrect 
              ? "bg-green-100 text-green-800 border border-green-200"
              : "bg-red-100 text-red-800 border border-red-200"
          )}>
            {isCorrect ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <AlertCircle className="h-5 w-5" />
            )}
            <span className="font-medium">
              {isCorrect ? "Correct! Well done!" : "Incorrect. Let's learn from this."}
            </span>
          </div>

          {/* Explanation */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 mb-2">Explanation</h4>
                <p className="text-blue-700 text-sm leading-relaxed">{data.explanation}</p>
              </div>
            </div>
          </div>

          {/* Try Again Button */}
          <div className="flex justify-center">
            <Button
              onClick={resetQuestion}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/5"
            >
              Try Another Question
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
