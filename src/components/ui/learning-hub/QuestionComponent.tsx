"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Check<PERSON><PERSON><PERSON>, XCircle, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface QuestionProps {
  question: string;
  type: "true_false" | "multiple_choice" | "explanation";
  options?: string[];
  correctAnswer: string;
  explanation: string;
  onComplete?: (isCorrect: boolean, userAnswer: string) => void;
}

export default function QuestionComponent({ 
  question, 
  type, 
  options, 
  correctAnswer, 
  explanation, 
  onComplete 
}: QuestionProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  const handleSubmit = () => {
    const correct = selectedAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
    setIsCorrect(correct);
    setShowResult(true);
    onComplete?.(correct, selectedAnswer);
  };

  const renderQuestionInput = () => {
    if (type === "true_false") {
      return (
        <div className="space-y-3">
          {["True", "False"].map((option) => (
            <motion.div
              key={option}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <label className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedAnswer === option
                  ? "border-primary bg-primary/10"
                  : "border-gray-200 hover:border-primary/50"
              }`}>
                <input
                  type="radio"
                  name="answer"
                  value={option}
                  checked={selectedAnswer === option}
                  onChange={(e) => setSelectedAnswer(e.target.value)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                  selectedAnswer === option
                    ? "border-primary bg-primary"
                    : "border-gray-300"
                }`}>
                  {selectedAnswer === option && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
                <span className="text-charcoal font-medium">{option}</span>
              </label>
            </motion.div>
          ))}
        </div>
      );
    }

    if (type === "multiple_choice" && options) {
      return (
        <div className="space-y-3">
          {options.map((option, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <label className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedAnswer === option
                  ? "border-primary bg-primary/10"
                  : "border-gray-200 hover:border-primary/50"
              }`}>
                <input
                  type="radio"
                  name="answer"
                  value={option}
                  checked={selectedAnswer === option}
                  onChange={(e) => setSelectedAnswer(e.target.value)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                  selectedAnswer === option
                    ? "border-primary bg-primary"
                    : "border-gray-300"
                }`}>
                  {selectedAnswer === option && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
                <span className="text-charcoal">{option}</span>
              </label>
            </motion.div>
          ))}
        </div>
      );
    }

    if (type === "explanation") {
      return (
        <div className="space-y-3">
          <Textarea
            placeholder="Type your explanation here..."
            value={selectedAnswer}
            onChange={(e) => setSelectedAnswer(e.target.value)}
            className="min-h-32 resize-none"
          />
        </div>
      );
    }

    return null;
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl border border-gray-200 p-6">
      {/* Question */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-3">
          <HelpCircle className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-charcoal">Question</h3>
        </div>
        <p className="text-charcoal leading-relaxed">{question}</p>
      </div>

      {/* Answer Input */}
      {!showResult && (
        <div className="mb-6">
          {renderQuestionInput()}
          
          <div className="mt-6 flex justify-end">
            <Button
              onClick={handleSubmit}
              disabled={!selectedAnswer.trim()}
              className="bg-primary hover:bg-primary-deep text-white"
            >
              Submit Answer
            </Button>
          </div>
        </div>
      )}

      {/* Result */}
      {showResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Result indicator */}
          <div className={`flex items-center gap-3 p-4 rounded-lg ${
            isCorrect 
              ? "bg-green-100 border border-green-200" 
              : "bg-red-100 border border-red-200"
          }`}>
            {isCorrect ? (
              <CheckCircle className="h-6 w-6 text-green-600" />
            ) : (
              <XCircle className="h-6 w-6 text-red-600" />
            )}
            <div>
              <div className={`font-semibold ${isCorrect ? "text-green-800" : "text-red-800"}`}>
                {isCorrect ? "Correct!" : "Incorrect"}
              </div>
              {!isCorrect && (
                <div className="text-sm text-red-700">
                  Correct answer: {correctAnswer}
                </div>
              )}
            </div>
          </div>

          {/* Your answer */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-charcoal mb-2">Your Answer:</h4>
            <p className="text-grey">{selectedAnswer}</p>
          </div>

          {/* Explanation */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-charcoal mb-2">Explanation:</h4>
            <p className="text-grey leading-relaxed">{explanation}</p>
          </div>
        </motion.div>
      )}
    </div>
  );
}
