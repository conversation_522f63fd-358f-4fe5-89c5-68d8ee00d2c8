"use client";

import { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { motion, stagger, useAnimate } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Brain,
  Target,
  GraduationCap,
  MessageCircle,
  X,
  Send,
  Loader2,
  AlertCircle,
  ArrowUp,
  Lightbulb,
  CreditCard as Cards,
  HelpCircle,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import FlashcardComponent from "@/components/ui/learning-hub/FlashcardComponent";
import QuestionComponent from "@/components/ui/learning-hub/QuestionComponent";
import TrueFalseComponent from "@/components/ui/learning-hub/TrueFalseComponent";
import PracticalComponent from "@/components/ui/learning-hub/PracticalComponent";

// Text Generate Effect Component
export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
}: {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}) => {
  const [scope, animate] = useAnimate();
  let wordsArray = words.split(" ");
  useEffect(() => {
    animate(
      "span",
      {
        opacity: 1,
        filter: filter ? "blur(0px)" : "none",
      },
      {
        duration: duration ? duration : 1,
        delay: stagger(0.2),
      }
    );
  }, [scope.current]);

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => {
          return (
            <motion.span
              key={word + idx}
              className="text-charcoal opacity-0"
              style={{
                filter: filter ? "blur(10px)" : "none",
              }}
            >
              {word}{" "}
            </motion.span>
          );
        })}
      </motion.div>
    );
  };

  return (
    <div className={cn("font-bold", className)}>
      <div className="mt-4">
        <div className="text-charcoal text-2xl leading-snug tracking-wide">
          {renderWords()}
        </div>
      </div>
    </div>
  );
};

// Types for AI Chat
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  mode?: 'assess' | 'tutor' | 'general';
  components?: MessageComponent[];
}

interface MessageComponent {
  type: 'flashcard' | 'question' | 'true_false' | 'practical' | 'mini_exam';
  data: any;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  lastUpdated: Date;
}

export default function LearningHub() {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [certificate, setCertificate] = useState<CertificateRecord | null>(null);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (certificateId) {
      loadCertificateData();
    }
  }, [certificateId]);

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadCertificateData = async () => {
    try {
      setLoading(true);
      const cert = await getCertificate(certificateId);
      setCertificate(cert);

      // Load saved chat sessions from localStorage
      const savedSessions = localStorage.getItem(`learning-hub-${certificateId}`);
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
      }
    } catch (error) {
      console.error("Error loading certificate:", error);
    } finally {
      setLoading(false);
    }
  };

  const saveChatSessions = (sessions: ChatSession[]) => {
    localStorage.setItem(`learning-hub-${certificateId}`, JSON.stringify(sessions));
    setChatSessions(sessions);
  };

  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: "New Learning Session",
      messages: [],
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    setCurrentSession(newSession);
    setShowWelcome(false);
  };

  const sendMessage = async (content: string, mode?: 'assess' | 'tutor') => {
    if (!content.trim() || !certificate) return;

    // Create user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date(),
      mode
    };

    // Update current session or create new one
    let session = currentSession;
    if (!session) {
      session = {
        id: Date.now().toString(),
        title: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
        messages: [],
        createdAt: new Date(),
        lastUpdated: new Date()
      };
      setCurrentSession(session);
      setShowWelcome(false);
    }

    // Add user message
    session.messages.push(userMessage);
    setCurrentSession({ ...session });
    setMessage("");
    setIsTyping(true);

    try {
      // Call AI API
      const response = await fetch('/api/ai/learning-hub', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          mode,
          certificate,
          conversationHistory: session.messages.slice(-10) // Last 10 messages for context
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const aiResponse = await response.json();

      // Create AI message
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        components: aiResponse.components
      };

      // Add AI message
      session.messages.push(aiMessage);
      session.lastUpdated = new Date();
      setCurrentSession({ ...session });

      // Save to localStorage
      const updatedSessions = chatSessions.filter(s => s.id !== session.id);
      updatedSessions.unshift(session);
      saveChatSessions(updatedSessions);

    } catch (error) {
      console.error("Error sending message:", error);

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again.",
        timestamp: new Date()
      };

      session.messages.push(errorMessage);
      setCurrentSession({ ...session });
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(message);
    }
  };

  if (loading) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-grey">Loading Learning Hub...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Failed to load certificate data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[700px] bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl overflow-hidden flex flex-col">
      {showWelcome && !currentSession ? (
        // Welcome Screen
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-2xl text-center space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <div className="bg-white rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center shadow-lg">
                <Brain className="h-12 w-12 text-primary" />
              </div>

              <TextGenerateEffect
                words="Welcome to your AI Learning Companion"
                className="text-3xl font-bold"
              />

              <p className="text-grey text-lg">
                Your personalized AI tutor for <span className="font-semibold text-primary">{certificate.name}</span>
              </p>
            </div>

            {/* Service Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all cursor-pointer group"
                onClick={() => {
                  createNewSession();
                  sendMessage("I want you to assess my knowledge", 'assess');
                }}
              >
                <div className="bg-accent/10 rounded-lg p-3 w-fit mb-4 group-hover:scale-110 transition-transform">
                  <Target className="h-8 w-8 text-accent" />
                </div>
                <h3 className="text-xl font-bold text-charcoal mb-2">Assess Me</h3>
                <p className="text-grey">
                  Test your knowledge with AI-generated questions and get personalized feedback on your strengths and weaknesses.
                </p>
                <div className="mt-4 flex items-center text-accent font-medium">
                  <span>Start Assessment</span>
                  <ArrowUp className="h-4 w-4 ml-2 rotate-45" />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all cursor-pointer group"
                onClick={() => {
                  createNewSession();
                  sendMessage("I want you to tutor me", 'tutor');
                }}
              >
                <div className="bg-primary/10 rounded-lg p-3 w-fit mb-4 group-hover:scale-110 transition-transform">
                  <GraduationCap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-charcoal mb-2">Tutor Me</h3>
                <p className="text-grey">
                  Learn with interactive lessons, flashcards, and practical explanations tailored to your learning style.
                </p>
                <div className="mt-4 flex items-center text-primary font-medium">
                  <span>Start Learning</span>
                  <ArrowUp className="h-4 w-4 ml-2 rotate-45" />
                </div>
              </motion.div>
            </div>

            {/* Features Preview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="bg-white/50 rounded-xl p-6 backdrop-blur-sm"
            >
              <h4 className="font-semibold text-charcoal mb-4">What makes this special?</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Cards className="h-4 w-4 text-primary" />
                  <span>Interactive Flashcards</span>
                </div>
                <div className="flex items-center gap-2">
                  <HelpCircle className="h-4 w-4 text-accent" />
                  <span>Mini Exams</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lightbulb className="h-4 w-4 text-primary" />
                  <span>Practical Examples</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-accent" />
                  <span>Instant Feedback</span>
                </div>
              </div>
            </motion.div>

            {/* Start General Chat */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.1 }}
            >
              <Button
                onClick={createNewSession}
                variant="outline"
                className="border-primary/20 text-primary hover:bg-primary/5"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Or start a general conversation
              </Button>
            </motion.div>
          </div>
        </div>
      ) : (
        // Chat Interface
        <div className="flex-1 flex flex-col bg-white rounded-2xl m-4 shadow-xl overflow-hidden">
          {/* Chat Header */}
          <div className="bg-gradient-to-r from-primary to-accent p-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 rounded-lg p-2">
                  <Brain className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold">{currentSession?.title || "AI Learning Companion"}</h3>
                  <p className="text-white/80 text-sm">{certificate.name}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={() => {
                  setCurrentSession(null);
                  setShowWelcome(true);
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {currentSession?.messages.map((msg) => (
              <div
                key={msg.id}
                className={cn(
                  "flex",
                  msg.type === 'user' ? "justify-end" : "justify-start"
                )}
              >
                <div
                  className={cn(
                    "max-w-[80%] rounded-2xl px-4 py-3",
                    msg.type === 'user'
                      ? "bg-primary text-white"
                      : "bg-gray-100 text-charcoal"
                  )}
                >
                  <p className="whitespace-pre-wrap">{msg.content}</p>

                  {/* Render interactive components */}
                  {msg.components && msg.components.map((component, index) => (
                    <div key={index} className="mt-4">
                      {component.type === 'flashcard' && (
                        <FlashcardComponent
                          data={component.data}
                          onComplete={(correct) => console.log('Flashcard completed:', correct)}
                        />
                      )}
                      {component.type === 'question' && (
                        <QuestionComponent
                          data={component.data}
                          onComplete={(correct) => console.log('Question completed:', correct)}
                        />
                      )}
                      {component.type === 'true_false' && (
                        <TrueFalseComponent
                          data={component.data}
                          onComplete={(correct) => console.log('True/False completed:', correct)}
                        />
                      )}
                      {component.type === 'practical' && (
                        <PracticalComponent
                          data={component.data}
                          onComplete={() => console.log('Practical completed')}
                        />
                      )}
                      {component.type === 'mini_exam' && (
                        <div className="bg-white/10 rounded-lg p-4 text-sm">
                          <p className="font-medium mb-2">Mini Exam</p>
                          <p>Questions: {component.data.questions?.length || 0}</p>
                          <p>Time Limit: {component.data.timeLimit || 'No limit'}</p>
                          <p>Passing Score: {component.data.passingScore || 70}%</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-2xl px-4 py-3 flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  <span className="text-grey">AI is thinking...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t p-4">
            <div className="flex gap-2">
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask me anything about your learning..."
                className="flex-1 min-h-[50px] max-h-[120px] resize-none border-gray-200 focus:border-primary"
                disabled={isTyping}
              />
              <Button
                onClick={() => sendMessage(message)}
                disabled={!message.trim() || isTyping}
                className="bg-primary hover:bg-primary-deep"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
