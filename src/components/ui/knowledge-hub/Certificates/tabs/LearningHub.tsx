"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getUserExamAttempts, type ExamAttempt } from "@/Services/examAttemptsService";
import { getQuestion, type QuestionRecord } from "@/Services/questionsService";
import { 
  BookOpen, 
  Brain, 
  Target, 
  TrendingUp, 
  Clock, 
  Award,
  ChevronRight,
  PlayCircle,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Lightbulb,
  GraduationCap
} from "lucide-react";

interface LearningStats {
  totalQuestions: number;
  correctAnswers: number;
  wrongAnswers: number;
  averageScore: number;
  totalAttempts: number;
  timeSpent: number;
  strongTopics: string[];
  weakTopics: string[];
}

export default function LearningHub() {
  const params = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [learningStats, setLearningStats] = useState<LearningStats | null>(null);

  const certificateId = Array.isArray(params?.framework) 
    ? params?.framework[0] 
    : (params?.framework as string);

  useEffect(() => {
    if (user && certificateId) {
      loadLearningData();
    }
  }, [user, certificateId]);

  const loadLearningData = async () => {
    if (!user || !certificateId) return;

    try {
      setLoading(true);
      setError(null);

      // Get all exam attempts for this user and certificate
      const attempts = await getUserExamAttempts(user.uid, certificateId);
      const completedAttempts = attempts.filter(attempt => attempt.status === "completed");

      if (completedAttempts.length === 0) {
        setLearningStats(null);
        return;
      }

      // Calculate learning statistics
      const totalQuestions = completedAttempts.reduce((sum, attempt) => sum + attempt.totalQuestions, 0);
      const correctAnswers = completedAttempts.reduce((sum, attempt) => sum + (attempt.correctAnswers || 0), 0);
      const wrongAnswers = totalQuestions - correctAnswers;
      const averageScore = completedAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0) / completedAttempts.length;
      const totalTimeSpent = completedAttempts.reduce((sum, attempt) => sum + (attempt.timeSpentSeconds || 0), 0);

      // Analyze topic performance (placeholder - would need topic data from questions)
      const strongTopics = ["Data Protection Principles", "GDPR Compliance"];
      const weakTopics = ["Data Subject Rights", "Controller Obligations"];

      setLearningStats({
        totalQuestions,
        correctAnswers,
        wrongAnswers,
        averageScore: Math.round(averageScore),
        totalAttempts: completedAttempts.length,
        timeSpent: totalTimeSpent,
        strongTopics,
        weakTopics
      });

    } catch (err) {
      console.error("Error loading learning data:", err);
      setError("Failed to load learning data");
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (loading) {
    return (
      <div className="w-full rounded-xl bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-grey">Loading learning insights...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full rounded-xl bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-500 mr-3" />
          <span className="text-red-600">{error}</span>
        </div>
      </div>
    );
  }

  if (!learningStats) {
    return (
      <div className="w-full space-y-6">
        {/* Welcome Section */}
        <div className="rounded-xl bg-gradient-to-br from-primary/10 to-accent/10 p-8 text-center">
          <div className="bg-white rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
            <GraduationCap className="h-12 w-12 text-primary" />
          </div>
          <h2 className="text-2xl font-bold text-charcoal mb-3">Welcome to Learning Hub</h2>
          <p className="text-grey text-lg mb-6">
            Start taking exams to unlock personalized learning insights and track your progress
          </p>
          <div className="bg-white rounded-lg p-4 inline-block">
            <p className="text-sm text-grey">Take your first exam to see:</p>
            <div className="flex items-center gap-4 mt-2 text-sm">
              <span className="flex items-center gap-1">
                <BarChart3 className="h-4 w-4 text-primary" />
                Performance Analytics
              </span>
              <span className="flex items-center gap-1">
                <Target className="h-4 w-4 text-accent" />
                Learning Recommendations
              </span>
              <span className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4 text-primary" />
                Progress Tracking
              </span>
            </div>
          </div>
        </div>

        {/* Getting Started Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="bg-primary/10 rounded-lg p-3 w-fit mb-4">
              <PlayCircle className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-semibold text-charcoal mb-2">Start Practicing</h3>
            <p className="text-grey text-sm">Take practice exams to build confidence and identify knowledge gaps</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="bg-accent/10 rounded-lg p-3 w-fit mb-4">
              <Brain className="h-6 w-6 text-accent" />
            </div>
            <h3 className="font-semibold text-charcoal mb-2">Learn Adaptively</h3>
            <p className="text-grey text-sm">Get personalized recommendations based on your performance</p>
          </div>
          
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="bg-primary/10 rounded-lg p-3 w-fit mb-4">
              <Award className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-semibold text-charcoal mb-2">Track Progress</h3>
            <p className="text-grey text-sm">Monitor your improvement and celebrate achievements</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="rounded-xl bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-2">
          <div className="bg-primary/10 rounded-xl p-2">
            <Brain className="h-6 w-6 text-primary" />
          </div>
          <h2 className="text-2xl font-semibold text-charcoal">Learning Hub</h2>
        </div>
        <p className="text-grey">
          Personalized insights and recommendations to accelerate your learning journey
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-xl p-6 shadow-sm text-center">
          <div className="text-2xl font-bold text-primary">{learningStats.totalAttempts}</div>
          <div className="text-sm text-grey">Exams Taken</div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm text-center">
          <div className="text-2xl font-bold text-accent">{learningStats.averageScore}%</div>
          <div className="text-sm text-grey">Average Score</div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm text-center">
          <div className="text-2xl font-bold text-charcoal">{learningStats.totalQuestions}</div>
          <div className="text-sm text-grey">Questions Answered</div>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm text-center">
          <div className="text-2xl font-bold text-primary">{formatTime(learningStats.timeSpent)}</div>
          <div className="text-sm text-grey">Time Studied</div>
        </div>
      </div>

      {/* Performance Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Accuracy Breakdown */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-charcoal mb-4 flex items-center gap-2">
            <Target className="h-5 w-5 text-primary" />
            Accuracy Breakdown
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-grey">Correct Answers</span>
              <span className="font-semibold text-accent">{learningStats.correctAnswers}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-accent to-primary h-2 rounded-full"
                style={{ width: `${(learningStats.correctAnswers / learningStats.totalQuestions) * 100}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-grey">Wrong Answers</span>
              <span className="font-semibold text-red-500">{learningStats.wrongAnswers}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-red-400 to-red-500 h-2 rounded-full"
                style={{ width: `${(learningStats.wrongAnswers / learningStats.totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Learning Recommendations */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-charcoal mb-4 flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-accent" />
            Learning Recommendations
          </h3>
          
          <div className="space-y-3">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Focus Areas</span>
              </div>
              <div className="space-y-1">
                {learningStats.weakTopics.map((topic, index) => (
                  <div key={index} className="text-sm text-red-700">• {topic}</div>
                ))}
              </div>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Strong Areas</span>
              </div>
              <div className="space-y-1">
                {learningStats.strongTopics.map((topic, index) => (
                  <div key={index} className="text-sm text-green-700">• {topic}</div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Items */}
      <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-charcoal mb-4">Recommended Next Steps</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white rounded-lg p-4 flex items-center gap-3 cursor-pointer hover:shadow-md transition-shadow">
            <div className="bg-red-100 rounded-lg p-2">
              <Target className="h-5 w-5 text-red-600" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-charcoal">Review Weak Topics</div>
              <div className="text-sm text-grey">Focus on areas needing improvement</div>
            </div>
            <ChevronRight className="h-5 w-5 text-grey" />
          </div>
          
          <div className="bg-white rounded-lg p-4 flex items-center gap-3 cursor-pointer hover:shadow-md transition-shadow">
            <div className="bg-primary/10 rounded-lg p-2">
              <PlayCircle className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-charcoal">Take Practice Exam</div>
              <div className="text-sm text-grey">Continue building knowledge</div>
            </div>
            <ChevronRight className="h-5 w-5 text-grey" />
          </div>
        </div>
      </div>
    </div>
  );
}
