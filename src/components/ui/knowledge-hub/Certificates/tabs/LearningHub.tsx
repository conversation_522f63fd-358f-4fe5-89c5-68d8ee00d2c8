"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useParams } from "next/navigation";

import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { motion, AnimatePresence } from "framer-motion";
import {
  Brain,
  ArrowUp,
  Target,
  GraduationCap,
  Loader2,
  AlertCircle,
  ChevronDown,
  Check,
  Plus,
  SlidersHorizontal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";


// Types
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  mode?: 'assess' | 'tutor' | 'chat';
}

interface LearningMode {
  id: 'assess' | 'tutor' | 'chat';
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

interface LearningSession {
  id: string;
  mode: 'assess' | 'tutor' | 'chat';
  messages: Message[];
  startTime: Date;
  lastActivity: Date;
}

// Constants
const LEARNING_MODES: LearningMode[] = [
  {
    id: 'assess',
    title: 'Assess Me',
    description: 'Test your knowledge with AI-generated questions tailored to your learning level',
    icon: <Target className="h-5 w-5" />,
    color: 'text-emerald-400',
    bgColor: 'bg-emerald-500/10'
  },
  {
    id: 'tutor',
    title: 'Tutor Me',
    description: 'Learn through interactive lessons, flashcards, and practical scenarios',
    icon: <GraduationCap className="h-5 w-5" />,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/10'
  },
  {
    id: 'chat',
    title: 'Chat',
    description: 'Ask me anything about the certification',
    icon: <Brain className="h-5 w-5" />,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/10'
  }
];

// Mode Selector Component
const ModeSelectorDropdown: React.FC<{
  modes: LearningMode[];
  selectedMode: string;
  onModeChange: (modeId: string) => void;
}> = ({ modes, selectedMode, onModeChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedModeData = modes.find((m) => m.id === selectedMode) || modes[0];
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        className="h-9 px-2.5 text-sm font-medium text-[#C2C0B6] hover:text-white hover:bg-[#3A3A37]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="truncate max-w-[150px] sm:max-w-[200px]">
          {selectedModeData.title}
        </span>
        <ChevronDown
          className={cn(
            "ml-1 h-4 w-4 transition-transform",
            isOpen && "rotate-180"
          )}
        />
      </Button>

      {isOpen && (
        <div className="absolute bottom-full right-0 mb-2 w-72 bg-[#3A3A37] border border-[#4A4A47] rounded-lg shadow-xl z-20 p-2">
          {modes.map((mode) => (
            <button
              key={mode.id}
              className={cn(
                "w-full text-left p-2.5 rounded-md hover:bg-[#4A4A47] transition-colors flex items-center justify-between",
                mode.id === selectedMode && "bg-[#4A4A47]"
              )}
              onClick={() => {
                onModeChange(mode.id);
                setIsOpen(false);
              }}
            >
              <div>
                <div className="flex items-center gap-2">
                  <span className={mode.color}>
                    {mode.icon}
                  </span>
                  <span className="font-medium text-[#C2C0B6]">
                    {mode.title}
                  </span>
                </div>
                <p className="text-xs text-[#8B8B85] mt-0.5">
                  {mode.description}
                </p>
              </div>
              {mode.id === selectedMode && (
                <Check className="h-4 w-4 text-[#D4AF37] flex-shrink-0" />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default function LearningHub() {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [certificate, setCertificate] = useState<CertificateRecord | null>(null);
  const [currentSession, setCurrentSession] = useState<LearningSession | null>(null);
  const [savedSessions, setSavedSessions] = useState<LearningSession[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'assess' | 'tutor' | 'chat'>('chat');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (certificateId) {
      loadCertificateData();
      loadSavedSessions();
    }
  }, [certificateId]);

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const maxHeight = 120;
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        maxHeight
      )}px`;
    }
  }, [inputMessage]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadCertificateData = async () => {
    try {
      setLoading(true);
      const cert = await getCertificate(certificateId);
      setCertificate(cert);
    } catch (error) {
      console.error("Error loading certificate:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedSessions = () => {
    const saved = localStorage.getItem(`learning-hub-sessions-${certificateId}`);
    if (saved) {
      setSavedSessions(JSON.parse(saved));
    }
  };

  const saveSession = (session: LearningSession) => {
    const updated = savedSessions.filter(s => s.id !== session.id);
    updated.unshift(session);
    setSavedSessions(updated.slice(0, 10)); // Keep only last 10 sessions
    localStorage.setItem(`learning-hub-sessions-${certificateId}`, JSON.stringify(updated.slice(0, 10)));
  };

  const startNewSession = (mode: 'assess' | 'tutor' | 'chat') => {
    const newSession: LearningSession = {
      id: Date.now().toString(),
      mode,
      messages: [],
      startTime: new Date(),
      lastActivity: new Date()
    };
    setCurrentSession(newSession);
    setSelectedMode(mode);
    setShowWelcome(false);
  };

  const handleModeChange = useCallback((modeId: string) => {
    setSelectedMode(modeId as 'assess' | 'tutor' | 'chat');
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === "Enter" && !e.shiftKey && !e.nativeEvent.isComposing) {
        e.preventDefault();
        sendMessage();
      }
    },
    [inputMessage]
  );

  const sendMessage = async () => {
    if (!inputMessage.trim() || isTyping || !certificate) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputMessage.trim(),
      timestamp: new Date(),
      mode: selectedMode
    };

    let session = currentSession;
    if (!session) {
      session = {
        id: Date.now().toString(),
        mode: selectedMode,
        messages: [],
        startTime: new Date(),
        lastActivity: new Date()
      };
      setCurrentSession(session);
      setShowWelcome(false);
    }

    const updatedSession = {
      ...session,
      messages: [...session.messages, userMessage],
      lastActivity: new Date()
    };
    setCurrentSession(updatedSession);
    setInputMessage("");
    setIsTyping(true);

    try {
      const response = await fetch("/api/ai/learning-hub", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          certificateId,
          message: userMessage.content,
          conversationHistory: updatedSession.messages,
          mode: selectedMode
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to get response");
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: data.response?.message || data.message || generateAIResponse(userMessage.content, selectedMode),
        timestamp: new Date(),
        mode: selectedMode
      };

      const finalSession = {
        ...updatedSession,
        messages: [...updatedSession.messages, aiMessage],
        lastActivity: new Date()
      };
      setCurrentSession(finalSession);
      saveSession(finalSession);

    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "Sorry, I encountered an error. Please try again.",
        timestamp: new Date(),
        mode: selectedMode
      };

      const errorSession = {
        ...updatedSession,
        messages: [...updatedSession.messages, errorMessage],
        lastActivity: new Date()
      };
      setCurrentSession(errorSession);
    } finally {
      setIsTyping(false);
    }
  };

  const generateAIResponse = (_userInput: string, mode: 'assess' | 'tutor' | 'chat'): string => {
    const responses = {
      assess: [
        "Let me create a personalized assessment for you based on your question...",
        "I'll generate some practice questions to test your understanding of this topic.",
        "Here's a quick assessment to evaluate your knowledge in this area."
      ],
      tutor: [
        "Let me break this down for you step by step...",
        "I'll create some interactive learning materials to help you understand this better.",
        "Here's a comprehensive explanation with examples and practice exercises."
      ],
      chat: [
        "That's a great question! Let me help you understand this concept.",
        "I'd be happy to explain that for you.",
        "Here's what you need to know about this topic."
      ]
    };

    const modeResponses = responses[mode];
    return modeResponses[Math.floor(Math.random() * modeResponses.length)];
  };

  const hasContent = inputMessage.trim();
  const canSend = hasContent && !isTyping;

  if (loading) {
    return (
      <div className="w-full h-96 flex items-center justify-center bg-[#262624] rounded-xl">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#D4AF37] mx-auto mb-4" />
          <p className="text-[#C2C0B6]">Loading Learning Hub...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="w-full h-96 flex items-center justify-center bg-[#262624] rounded-xl">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Certificate not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-[#262624] rounded-xl p-6">
      <div className="w-full max-w-4xl mx-auto">
        {showWelcome ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8 text-center py-8"
          >
            <div className="mb-8">
              <div className="bg-[#3A3A37] rounded-full p-6 w-20 h-20 mx-auto flex items-center justify-center shadow-lg mb-6">
                <Brain className="h-10 w-10 text-[#D4AF37]" />
              </div>
              <h1 className="text-2xl font-serif font-light text-[#C2C0B6] mb-2">
                Welcome to your AI-powered learning companion
              </h1>
              <p className="text-lg text-[#8B8B85] max-w-2xl mx-auto">
                I'm here to help you master {certificate.name} through personalized assessments and interactive tutoring
              </p>
            </div>

            {/* Action Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto mb-8">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => startNewSession("assess")}
                className="bg-[#3A3A37] rounded-xl p-4 shadow-lg cursor-pointer border border-[#4A4A47] hover:border-[#D4AF37]/30 transition-all"
              >
                <div className="bg-emerald-500/10 rounded-lg p-3 w-fit mb-3">
                  <Target className="h-6 w-6 text-emerald-400" />
                </div>
                <h3 className="text-lg font-bold text-[#C2C0B6] mb-2">Assess Me</h3>
                <p className="text-sm text-[#8B8B85] mb-3">
                  Test your knowledge with AI-generated questions tailored to your learning level
                </p>
                <div className="flex items-center text-emerald-400 font-medium text-sm">
                  Start Assessment
                  <ArrowUp className="h-4 w-4 ml-2 rotate-45" />
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => startNewSession("tutor")}
                className="bg-[#3A3A37] rounded-xl p-4 shadow-lg cursor-pointer border border-[#4A4A47] hover:border-[#D4AF37]/30 transition-all"
              >
                <div className="bg-blue-500/10 rounded-lg p-3 w-fit mb-3">
                  <GraduationCap className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-lg font-bold text-[#C2C0B6] mb-2">Tutor Me</h3>
                <p className="text-sm text-[#8B8B85] mb-3">
                  Learn through interactive lessons, flashcards, and practical scenarios
                </p>
                <div className="flex items-center text-blue-400 font-medium text-sm">
                  Start Learning
                  <ArrowUp className="h-4 w-4 ml-2 rotate-45" />
                </div>
              </motion.div>
            </div>
          </motion.div>
        ) : null}

        {/* Modern Chat Input */}
        <div className="bg-[#30302E] border border-[#4A4A47] rounded-xl shadow-lg items-end gap-2 min-h-[150px] flex flex-col">
          <textarea
            ref={textareaRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me anything about the certification..."
            disabled={isTyping}
            className="flex-1 min-h-[100px] w-full p-4 focus-within:border-none focus:outline-none focus:border-none border-none outline-none focus-within:ring-0 focus-within:ring-offset-0 focus-within:outline-none max-h-[120px] resize-none border-0 bg-transparent text-[#C2C0B6] shadow-none focus-visible:ring-0 placeholder:text-[#8B8B85] text-sm sm:text-base"
            rows={1}
          />
          <div className="flex items-center gap-2 justify-between w-full px-3 pb-1.5">
            <div className="flex items-center gap-2">
              <Button
                size="icon"
                variant="ghost"
                className="h-9 w-9 p-0 text-[#8B8B85] hover:text-[#C2C0B6] hover:bg-[#3A3A37] flex-shrink-0"
                disabled={isTyping}
                title="Attach files"
              >
                <Plus className="h-5 w-5" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="h-9 w-9 p-0 text-[#8B8B85] hover:text-[#C2C0B6] hover:bg-[#3A3A37] flex-shrink-0"
                disabled={isTyping}
                title="Options"
              >
                <SlidersHorizontal className="h-5 w-5" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <ModeSelectorDropdown
                modes={LEARNING_MODES}
                selectedMode={selectedMode}
                onModeChange={handleModeChange}
              />

              <Button
                size="icon"
                className={cn(
                  "h-9 w-9 p-0 flex-shrink-0 rounded-md transition-colors",
                  canSend
                    ? "bg-[#D4AF37] hover:bg-[#B8941F] text-white"
                    : "bg-[#3A3A37] text-[#8B8B85] cursor-not-allowed"
                )}
                onClick={sendMessage}
                disabled={!canSend}
                title="Send message"
              >
                <ArrowUp className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        {currentSession && currentSession.messages.length > 0 && (
          <div className="mt-6 space-y-4 max-h-80 overflow-y-auto pr-2">
            <AnimatePresence>
              {currentSession.messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div className={cn(
                    "max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm",
                    message.type === "user"
                      ? "bg-[#D4AF37] text-white rounded-br-md"
                      : "bg-[#3A3A37] text-[#C2C0B6] rounded-bl-md border border-[#4A4A47]"
                  )}>
                    <p className="text-sm leading-relaxed">{message.content}</p>
                    <div className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Typing Indicator */}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="bg-[#3A3A37] text-[#C2C0B6] rounded-lg rounded-bl-md px-4 py-3 max-w-xs border border-[#4A4A47]">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-[#D4AF37]" />
                    <span className="text-[#8B8B85] text-sm">AI is thinking...</span>
                  </div>
                </div>
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
}
