"use client";

import { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { motion, stagger, useAnimate } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Target,
  GraduationCap,
  MessageCircle,
  X,
  Send,
  Loader2,
  AlertCircle,
  ArrowUp,
  Lightbulb,
  CreditCard as Cards,
  HelpCircle,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import FlashcardComponent from "@/components/ui/learning-hub/FlashcardComponent";
import QuestionComponent from "@/components/ui/learning-hub/QuestionComponent";
import TrueFalseComponent from "@/components/ui/learning-hub/TrueFalseComponent";
import PracticalComponent from "@/components/ui/learning-hub/PracticalComponent";

// Text Generate Effect Component
export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
}: {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}) => {
  const [scope, animate] = useAnimate();
  let wordsArray = words.split(" ");
  useEffect(() => {
    animate(
      "span",
      {
        opacity: 1,
        filter: filter ? "blur(0px)" : "none",
      },
      {
        duration: duration ? duration : 1,
        delay: stagger(0.2),
      }
    );
  }, [scope.current]);

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => {
          return (
            <motion.span
              key={word + idx}
              className="text-charcoal opacity-0"
              style={{
                filter: filter ? "blur(10px)" : "none",
              }}
            >
              {word}{" "}
            </motion.span>
          );
        })}
      </motion.div>
    );
  };

  return (
    <div className={cn("font-bold", className)}>
      <div className="mt-4">
        <div className="text-charcoal text-2xl leading-snug tracking-wide">
          {renderWords()}
        </div>
      </div>
    </div>
  );
};

// Types for AI Chat
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  mode?: 'assess' | 'tutor' | 'general';
  components?: MessageComponent[];
}

interface MessageComponent {
  type: 'flashcard' | 'question' | 'true_false' | 'practical' | 'mini_exam';
  data: any;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  lastUpdated: Date;
}

export default function LearningHub() {
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [certificate, setCertificate] = useState<CertificateRecord | null>(null);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (certificateId) {
      loadCertificateData();
    }
  }, [certificateId]);

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadCertificateData = async () => {
    try {
      setLoading(true);
      const cert = await getCertificate(certificateId);
      setCertificate(cert);

      // Load saved chat sessions from localStorage
      const savedSessions = localStorage.getItem(`learning-hub-${certificateId}`);
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
      }
    } catch (error) {
      console.error("Error loading certificate:", error);
    } finally {
      setLoading(false);
    }
  };

  const saveChatSessions = (sessions: ChatSession[]) => {
    localStorage.setItem(`learning-hub-${certificateId}`, JSON.stringify(sessions));
    setChatSessions(sessions);
  };

  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: "New Learning Session",
      messages: [],
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    setCurrentSession(newSession);
    setShowWelcome(false);
  };

  const sendMessage = async (content: string, mode?: 'assess' | 'tutor') => {
    if (!content.trim() || !certificate) return;

    // Create user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date(),
      mode
    };

    // Update current session or create new one
    let session = currentSession;
    if (!session) {
      session = {
        id: Date.now().toString(),
        title: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
        messages: [],
        createdAt: new Date(),
        lastUpdated: new Date()
      };
      setCurrentSession(session);
      setShowWelcome(false);
    }

    // Add user message
    session.messages.push(userMessage);
    setCurrentSession({ ...session });
    setMessage("");
    setIsTyping(true);

    try {
      // Call AI API
      const response = await fetch('/api/ai/learning-hub', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          mode,
          certificate,
          conversationHistory: session.messages.slice(-10) // Last 10 messages for context
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const aiResponse = await response.json();

      // Create AI message
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        components: aiResponse.components
      };

      // Add AI message
      session.messages.push(aiMessage);
      session.lastUpdated = new Date();
      setCurrentSession({ ...session });

      // Save to localStorage
      const updatedSessions = chatSessions.filter(s => s.id !== session.id);
      updatedSessions.unshift(session);
      saveChatSessions(updatedSessions);

    } catch (error) {
      console.error("Error sending message:", error);

      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again.",
        timestamp: new Date()
      };

      session.messages.push(errorMessage);
      setCurrentSession({ ...session });
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(message);
    }
  };

  if (loading) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-grey">Loading Learning Hub...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="w-full h-[600px] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Failed to load certificate data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white">
      {showWelcome && !currentSession ? (
        // Welcome Screen - DTC Professional Design
        <div className="max-w-6xl mx-auto px-6 py-12">
          {/* Header Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary rounded-2xl mb-8">
              <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>

            <TextGenerateEffect
              words="AI Learning Companion"
              className="text-4xl font-bold mb-4"
            />

            <p className="text-xl text-grey max-w-2xl mx-auto">
              Your personalized AI tutor for <span className="font-semibold text-primary">{certificate.name}</span> certification
            </p>
          </div>

          {/* Main Service Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="group cursor-pointer"
              onClick={() => {
                createNewSession();
                sendMessage("I want you to assess my knowledge", 'assess');
              }}
            >
              <div className="bg-white border border-gray-200 rounded-2xl p-8 hover:border-accent hover:shadow-lg transition-all duration-300">
                <div className="flex items-start gap-6">
                  <div className="bg-accent/10 rounded-xl p-4 group-hover:bg-accent/20 transition-colors">
                    <Target className="h-8 w-8 text-accent" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-charcoal mb-3">Knowledge Assessment</h3>
                    <p className="text-grey text-lg leading-relaxed mb-6">
                      Evaluate your understanding with AI-generated questions tailored to your certification requirements. Get detailed feedback on strengths and areas for improvement.
                    </p>
                    <div className="inline-flex items-center text-accent font-semibold group-hover:gap-3 gap-2 transition-all">
                      <span>Begin Assessment</span>
                      <ArrowUp className="h-5 w-5 rotate-45 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="group cursor-pointer"
              onClick={() => {
                createNewSession();
                sendMessage("I want you to tutor me", 'tutor');
              }}
            >
              <div className="bg-white border border-gray-200 rounded-2xl p-8 hover:border-primary hover:shadow-lg transition-all duration-300">
                <div className="flex items-start gap-6">
                  <div className="bg-primary/10 rounded-xl p-4 group-hover:bg-primary/20 transition-colors">
                    <GraduationCap className="h-8 w-8 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-charcoal mb-3">Interactive Tutoring</h3>
                    <p className="text-grey text-lg leading-relaxed mb-6">
                      Learn through interactive lessons, flashcards, and practical scenarios. Receive step-by-step guidance adapted to your learning pace and style.
                    </p>
                    <div className="inline-flex items-center text-primary font-semibold group-hover:gap-3 gap-2 transition-all">
                      <span>Start Learning</span>
                      <ArrowUp className="h-5 w-5 rotate-45 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-gray-50 rounded-2xl p-8 mb-12"
          >
            <h4 className="text-xl font-bold text-charcoal text-center mb-8">Advanced Learning Features</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Cards className="h-6 w-6 text-primary mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Interactive Flashcards</h5>
                <p className="text-sm text-grey">Dynamic learning cards</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <HelpCircle className="h-6 w-6 text-accent mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Mini Assessments</h5>
                <p className="text-sm text-grey">Quick knowledge checks</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Lightbulb className="h-6 w-6 text-primary mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Practical Examples</h5>
                <p className="text-sm text-grey">Real-world scenarios</p>
              </div>
              <div className="text-center">
                <div className="bg-white rounded-xl p-4 mb-3 shadow-sm">
                  <Zap className="h-6 w-6 text-accent mx-auto" />
                </div>
                <h5 className="font-semibold text-charcoal mb-1">Instant Feedback</h5>
                <p className="text-sm text-grey">Immediate responses</p>
              </div>
            </div>
          </motion.div>

          {/* Alternative Option */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="text-center"
          >
            <p className="text-grey mb-4">Need something specific?</p>
            <Button
              onClick={createNewSession}
              variant="outline"
              size="lg"
              className="border-gray-300 text-charcoal hover:border-primary hover:text-primary"
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              Start a Custom Conversation
            </Button>
          </motion.div>
        </div>
      ) : (
        // Chat Interface - Professional DTC Design
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-5xl mx-auto">
            {/* Professional Header */}
            <div className="bg-white border-b border-gray-200 px-8 py-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-br from-primary to-primary-deep rounded-xl p-3 shadow-sm">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-charcoal tracking-tight">
                      {currentSession?.title || "AI Learning Companion"}
                    </h1>
                    <p className="text-grey font-medium">{certificate.name} • Professional Certification Training</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-sm text-grey">Session Active</p>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-grey">AI Ready</span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-grey hover:text-charcoal hover:bg-gray-100 rounded-lg"
                    onClick={() => {
                      setCurrentSession(null);
                      setShowWelcome(true);
                    }}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages Container - Professional Layout */}
            <div className="flex-1 bg-white">
              <div className="max-h-[calc(100vh-300px)] overflow-y-auto px-8 py-6">
                <div className="space-y-8">
                  {currentSession?.messages.map((msg) => (
                    <div key={msg.id} className="group">
                      {msg.type === 'user' ? (
                        // User Message - Professional Style
                        <div className="flex justify-end">
                          <div className="max-w-[80%]">
                            <div className="bg-primary text-white rounded-2xl px-6 py-4 shadow-sm">
                              <p className="leading-relaxed">{msg.content}</p>
                            </div>
                            <p className="text-xs text-grey mt-2 text-right">
                              {new Date(msg.timestamp).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      ) : (
                        // AI Message - Professional Style
                        <div className="flex justify-start">
                          <div className="max-w-[85%]">
                            <div className="flex items-start gap-4">
                              <div className="bg-gradient-to-br from-primary to-primary-deep rounded-xl p-2 shadow-sm flex-shrink-0 mt-1">
                                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                              </div>
                              <div className="flex-1">
                                <div className="bg-gray-50 border border-gray-200 rounded-2xl px-6 py-4">
                                  <p className="text-charcoal leading-relaxed whitespace-pre-wrap">{msg.content}</p>
                                </div>

                                {/* Interactive Components */}
                                {msg.components && msg.components.length > 0 && (
                                  <div className="mt-4 space-y-4">
                                    {msg.components.map((component, compIndex) => (
                                      <div key={compIndex} className="bg-white border border-gray-200 rounded-xl p-1 shadow-sm">
                                        {component.type === 'flashcard' && (
                                          <FlashcardComponent
                                            data={component.data}
                                            onComplete={(correct) => console.log('Flashcard completed:', correct)}
                                          />
                                        )}
                                        {component.type === 'question' && (
                                          <QuestionComponent
                                            data={component.data}
                                            onComplete={(correct) => console.log('Question completed:', correct)}
                                          />
                                        )}
                                        {component.type === 'true_false' && (
                                          <TrueFalseComponent
                                            data={component.data}
                                            onComplete={(correct) => console.log('True/False completed:', correct)}
                                          />
                                        )}
                                        {component.type === 'practical' && (
                                          <PracticalComponent
                                            data={component.data}
                                            onComplete={() => console.log('Practical completed')}
                                          />
                                        )}
                                        {component.type === 'mini_exam' && (
                                          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                                            <h4 className="font-semibold text-blue-800 mb-3">Assessment Ready</h4>
                                            <div className="space-y-2 text-blue-700 text-sm">
                                              <p>Questions: {component.data.questions?.length || 0}</p>
                                              <p>Time Limit: {component.data.timeLimit || 'No limit'}</p>
                                              <p>Passing Score: {component.data.passingScore || 70}%</p>
                                            </div>
                                            <Button className="mt-4 bg-primary hover:bg-primary-deep text-white">
                                              Start Assessment
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                )}

                                <p className="text-xs text-grey mt-2">
                                  AI Response • {new Date(msg.timestamp).toLocaleTimeString()}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="flex items-start gap-4">
                        <div className="bg-gradient-to-br from-primary to-primary-deep rounded-xl p-2 shadow-sm flex-shrink-0">
                          <Loader2 className="w-5 h-5 text-white animate-spin" />
                        </div>
                        <div className="bg-gray-50 border border-gray-200 rounded-2xl px-6 py-4">
                          <div className="flex items-center gap-3">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                            </div>
                            <span className="text-grey text-sm">AI is analyzing your request...</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </div>
            </div>

            {/* Professional Input Section */}
            <div className="bg-white border-t border-gray-200 px-8 py-6">
              <div className="max-w-4xl mx-auto">
                <div className="bg-gray-50 border border-gray-300 rounded-2xl p-4 focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20 transition-all">
                  <div className="flex items-end gap-4">
                    <div className="flex-1">
                      <Textarea
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Ask me anything about your certification journey..."
                        className="w-full min-h-[60px] max-h-[120px] resize-none border-0 bg-transparent text-charcoal placeholder:text-grey focus:ring-0 focus:border-0 text-base leading-relaxed"
                        disabled={isTyping}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => sendMessage(message)}
                        disabled={!message.trim() || isTyping}
                        size="lg"
                        className={cn(
                          "rounded-xl px-6 py-3 font-semibold transition-all",
                          message.trim() && !isTyping
                            ? "bg-primary hover:bg-primary-deep text-white shadow-sm hover:shadow-md"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        )}
                      >
                        {isTyping ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <Send className="h-5 w-5" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between mt-3 px-2">
                  <p className="text-xs text-grey">
                    Press Enter to send • Shift + Enter for new line
                  </p>
                  <div className="flex items-center gap-2 text-xs text-grey">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>AI Ready</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
