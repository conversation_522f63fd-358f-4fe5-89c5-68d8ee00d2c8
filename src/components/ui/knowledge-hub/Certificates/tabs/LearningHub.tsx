"use client";

import { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import { motion, AnimatePresence } from "framer-motion";
import {
  Brain,
  Send,
  Sparkles,
  Target,
  GraduationCap,
  MessageCircle,
  Loader2,
  BookOpen,
  CheckCircle,
  AlertCircle,
  Save,
  History
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { TextGenerateEffect } from "@/components/ui/effects/TextGenerateEffect";
import FlashcardComponent from "@/components/ui/learning-hub/FlashcardComponent";
import QuestionComponent from "@/components/ui/learning-hub/QuestionComponent";
import PracticalComponent from "@/components/ui/learning-hub/PracticalComponent";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  type?: "text" | "flashcard" | "question" | "practical" | "assessment";
  data?: any;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  mode?: "assess" | "tutor" | "general";
}

export default function LearningHub() {
  const params = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [certificate, setCertificate] = useState<CertificateRecord | null>(null);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [savedSessions, setSavedSessions] = useState<ChatSession[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (certificateId) {
      loadCertificateData();
      loadSavedSessions();
    }
  }, [certificateId]);

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadCertificateData = async () => {
    try {
      setLoading(true);
      const cert = await getCertificate(certificateId);
      setCertificate(cert);
    } catch (error) {
      console.error("Error loading certificate:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedSessions = () => {
    const saved = localStorage.getItem(`learning-hub-sessions-${certificateId}`);
    if (saved) {
      setSavedSessions(JSON.parse(saved));
    }
  };

  const saveSession = (session: ChatSession) => {
    const updated = savedSessions.filter(s => s.id !== session.id);
    updated.unshift(session);
    setSavedSessions(updated.slice(0, 10)); // Keep only last 10 sessions
    localStorage.setItem(`learning-hub-sessions-${certificateId}`, JSON.stringify(updated.slice(0, 10)));
  };

  const startNewSession = (mode?: "assess" | "tutor") => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: mode ? `${mode === "assess" ? "Assessment" : "Tutoring"} Session` : "General Chat",
      messages: [],
      createdAt: Date.now(),
      mode
    };
    setCurrentSession(newSession);
    setShowWelcome(false);
  };

  const sendMessage = async (message: string, mode?: "assess" | "tutor") => {
    if (!message.trim() || !certificate) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: message,
      timestamp: Date.now()
    };

    let session = currentSession;
    if (!session) {
      session = {
        id: Date.now().toString(),
        title: message.slice(0, 50) + (message.length > 50 ? "..." : ""),
        messages: [],
        createdAt: Date.now(),
        mode
      };
      setCurrentSession(session);
      setShowWelcome(false);
    }

    const updatedSession = {
      ...session,
      messages: [...session.messages, userMessage]
    };
    setCurrentSession(updatedSession);
    setInputMessage("");
    setIsTyping(true);

    try {
      const response = await fetch("/api/ai/learning-hub", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          certificateId,
          message,
          conversationHistory: updatedSession.messages,
          mode: mode || session.mode
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to get response");
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.response.message,
        timestamp: Date.now(),
        type: data.response.type,
        data: data.response
      };

      const finalSession = {
        ...updatedSession,
        messages: [...updatedSession.messages, assistantMessage],
        mode: data.mode
      };

      setCurrentSession(finalSession);
      saveSession(finalSession);

    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "Sorry, I encountered an error. Please try again.",
        timestamp: Date.now()
      };

      const errorSession = {
        ...updatedSession,
        messages: [...updatedSession.messages, errorMessage]
      };
      setCurrentSession(errorSession);
    } finally {
      setIsTyping(false);
    }
  };

  const renderMessageContent = (message: Message) => {
    if (message.role === "user") {
      return (
        <div className="bg-primary text-white rounded-2xl rounded-br-md px-4 py-3 max-w-xs ml-auto">
          {message.content}
        </div>
      );
    }

    // Assistant messages
    const baseContent = (
      <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 max-w-2xl">
        <TextGenerateEffect words={message.content} className="text-charcoal text-sm" />
      </div>
    );

    if (message.type === "flashcard" && message.data?.content?.flashcard) {
      return (
        <div className="space-y-4 max-w-2xl">
          {baseContent}
          <FlashcardComponent
            front={message.data.content.flashcard.front}
            back={message.data.content.flashcard.back}
          />
        </div>
      );
    }

    if (message.type === "question" && message.data?.content?.question) {
      return (
        <div className="space-y-4 max-w-2xl">
          {baseContent}
          <QuestionComponent
            question={message.data.content.question.question}
            type={message.data.content.question.type}
            options={message.data.content.question.options}
            correctAnswer={message.data.content.question.correctAnswer}
            explanation={message.data.content.question.explanation}
          />
        </div>
      );
    }

    if (message.type === "practical" && message.data?.content?.practical) {
      return (
        <div className="space-y-4 max-w-2xl">
          {baseContent}
          <PracticalComponent
            scenario={message.data.content.practical.scenario}
            steps={message.data.content.practical.steps}
            keyPoints={message.data.content.practical.keyPoints}
          />
        </div>
      );
    }

    return baseContent;
  };

  if (loading) {
    return (
      <div className="w-full h-96 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-grey">Loading Learning Hub...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="w-full h-96 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">Certificate not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col bg-gradient-to-br from-primary/5 to-accent/5 rounded-xl overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-br from-primary to-accent rounded-xl p-3">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-charcoal">AI Learning Hub</h2>
              <p className="text-grey">Your intelligent study partner for {certificate.name}</p>
            </div>
          </div>

          {currentSession && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (currentSession) saveSession(currentSession);
                  setCurrentSession(null);
                  setShowWelcome(true);
                }}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save & New
              </Button>

              {savedSessions.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <History className="h-4 w-4" />
                  History ({savedSessions.length})
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Welcome Screen */}
      {showWelcome && (
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-8"
            >
              {/* Hero Section */}
              <div className="space-y-4">
                <div className="bg-white rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center shadow-lg">
                  <Sparkles className="h-12 w-12 text-primary" />
                </div>
                <TextGenerateEffect
                  words="Welcome to your AI-powered learning companion"
                  className="text-3xl font-bold text-charcoal"
                />
                <p className="text-xl text-grey max-w-2xl mx-auto">
                  I'm here to help you master {certificate.name} through personalized assessments and interactive tutoring
                </p>
              </div>

              {/* Action Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => startNewSession("assess")}
                  className="bg-white rounded-xl p-6 shadow-lg cursor-pointer border-2 border-transparent hover:border-primary/30 transition-all"
                >
                  <div className="bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg p-4 w-fit mb-4">
                    <Target className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-charcoal mb-2">Assess Me</h3>
                  <p className="text-grey mb-4">
                    Test your knowledge with AI-generated questions tailored to your learning level
                  </p>
                  <div className="flex items-center text-primary font-medium">
                    Start Assessment
                    <MessageCircle className="h-4 w-4 ml-2" />
                  </div>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => startNewSession("tutor")}
                  className="bg-white rounded-xl p-6 shadow-lg cursor-pointer border-2 border-transparent hover:border-accent/30 transition-all"
                >
                  <div className="bg-gradient-to-br from-accent/10 to-primary/10 rounded-lg p-4 w-fit mb-4">
                    <GraduationCap className="h-8 w-8 text-accent" />
                  </div>
                  <h3 className="text-xl font-bold text-charcoal mb-2">Tutor Me</h3>
                  <p className="text-grey mb-4">
                    Learn through interactive lessons, flashcards, and practical scenarios
                  </p>
                  <div className="flex items-center text-accent font-medium">
                    Start Learning
                    <MessageCircle className="h-4 w-4 ml-2" />
                  </div>
                </motion.div>
              </div>

              {/* Quick Start */}
              <div className="bg-white rounded-xl p-6 max-w-xl mx-auto">
                <h4 className="font-semibold text-charcoal mb-3">Or just start chatting</h4>
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Ask me anything about the certification..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage(inputMessage);
                      }
                    }}
                    className="flex-1 min-h-12 resize-none"
                  />
                  <Button
                    onClick={() => sendMessage(inputMessage)}
                    disabled={!inputMessage.trim()}
                    className="bg-primary hover:bg-primary-deep text-white px-4"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}

      {/* Chat Interface */}
      {currentSession && !showWelcome && (
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            <AnimatePresence>
              {currentSession.messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  {renderMessageContent(message)}
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Typing Indicator */}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3 max-w-xs">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                    <span className="text-grey text-sm">AI is thinking...</span>
                  </div>
                </div>
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="flex gap-3">
              <Textarea
                placeholder="Type your message..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(inputMessage);
                  }
                }}
                className="flex-1 min-h-12 max-h-32 resize-none"
                disabled={isTyping}
              />
              <Button
                onClick={() => sendMessage(inputMessage)}
                disabled={!inputMessage.trim() || isTyping}
                className="bg-primary hover:bg-primary-deep text-white px-6"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
