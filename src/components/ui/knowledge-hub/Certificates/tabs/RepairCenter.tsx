"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useUser } from "@/hooks/useUser";
import { getUserExamAttempts, type ExamAttempt } from "@/Services/examAttemptsService";
import { getQuestion, type QuestionRecord } from "@/Services/questionsService";
import { AlertTriangle, BookOpen, Target, TrendingDown } from "lucide-react";

interface WrongQuestionData {
  questionId: string;
  question: QuestionRecord | null;
  wrongCount: number;
  attempts: string[]; // attempt IDs where this question was wrong
}

export default function RepairCenter() {
  const params = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [wrongQuestions, setWrongQuestions] = useState<WrongQuestionData[]>([]);
  const [totalAttempts, setTotalAttempts] = useState(0);

  const certificateId = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string);

  useEffect(() => {
    if (user && certificateId) {
      loadWrongQuestions();
    }
  }, [user, certificateId]);

  const loadWrongQuestions = async () => {
    if (!user || !certificateId) return;

    try {
      setLoading(true);
      setError(null);

      // Get all exam attempts for this user and certificate
      const attempts = await getUserExamAttempts(user.uid, certificateId);
      const completedAttempts = attempts.filter(attempt => attempt.status === "completed");

      setTotalAttempts(completedAttempts.length);

      if (completedAttempts.length === 0) {
        setWrongQuestions([]);
        return;
      }

      // Collect all wrong answers across all attempts
      const wrongAnswersMap = new Map<string, { count: number; attempts: string[] }>();

      completedAttempts.forEach(attempt => {
        attempt.answers.forEach(answer => {
          if (!answer.isCorrect) {
            const existing = wrongAnswersMap.get(answer.questionId);
            if (existing) {
              existing.count++;
              if (!existing.attempts.includes(attempt.id)) {
                existing.attempts.push(attempt.id);
              }
            } else {
              wrongAnswersMap.set(answer.questionId, {
                count: 1,
                attempts: [attempt.id]
              });
            }
          }
        });
      });

      // Load question details for each wrong question
      const wrongQuestionPromises = Array.from(wrongAnswersMap.entries()).map(
        async ([questionId, data]) => {
          const question = await getQuestion(certificateId, questionId);
          return {
            questionId,
            question,
            wrongCount: data.count,
            attempts: data.attempts
          };
        }
      );

      const wrongQuestionsData = await Promise.all(wrongQuestionPromises);

      // Sort by wrong count (most wrong first), then by question text
      const sortedWrongQuestions = wrongQuestionsData
        .filter(item => item.question !== null) // Filter out questions that couldn't be loaded
        .sort((a, b) => {
          if (b.wrongCount !== a.wrongCount) {
            return b.wrongCount - a.wrongCount;
          }
          return (a.question?.question || "").localeCompare(b.question?.question || "");
        });

      setWrongQuestions(sortedWrongQuestions);
    } catch (err) {
      console.error("Error loading wrong questions:", err);
      setError("Failed to load repair center data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-grey">Loading repair center...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="flex items-center justify-center py-12">
          <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
          <span className="text-red-600">{error}</span>
        </div>
      </div>
    );
  }

  if (totalAttempts === 0) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-grey mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-charcoal mb-2">No Exam Attempts Yet</h3>
          <p className="text-grey">Complete some exams first to see questions that need repair.</p>
        </div>
      </div>
    );
  }

  if (wrongQuestions.length === 0) {
    return (
      <div className="w-full rounded-xl border border-gray-100 bg-white p-8 shadow-sm">
        <div className="text-center py-12">
          <Target className="h-16 w-16 text-accent mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-charcoal mb-2">Perfect Performance!</h3>
          <p className="text-grey">You haven't gotten any questions wrong across {totalAttempts} attempt{totalAttempts !== 1 ? 's' : ''}.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="rounded-xl border border-gray-100 bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-2">
          <div className="bg-red-50 rounded-xl p-2">
            <TrendingDown className="h-6 w-6 text-red-600" />
          </div>
          <h2 className="text-2xl font-semibold text-charcoal">Repair Center</h2>
        </div>
        <p className="text-grey">
          Questions you got wrong across {totalAttempts} exam attempt{totalAttempts !== 1 ? 's' : ''}.
          Focus on these areas to improve your performance.
        </p>
        <div className="mt-4 flex items-center gap-4 text-sm">
          <span className="text-charcoal font-medium">{wrongQuestions.length} unique questions need attention</span>
          <span className="text-grey">•</span>
          <span className="text-grey">
            {wrongQuestions.reduce((sum, q) => sum + q.wrongCount, 0)} total wrong answers
          </span>
        </div>
      </div>

      {/* Wrong Questions List */}
      <div className="rounded-xl border border-gray-100 bg-white shadow-sm">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-charcoal">Questions to Review</h3>
          <p className="text-sm text-grey mt-1">Ordered by frequency of wrong answers</p>
        </div>

        <div className="divide-y divide-gray-100">
          {wrongQuestions.map((item, index) => (
            <div key={item.questionId} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-start gap-4">
                {/* Question Number */}
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-red-100 text-red-600 font-semibold text-sm flex items-center justify-center">
                    {index + 1}
                  </div>
                </div>

                {/* Question Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Wrong {item.wrongCount} time{item.wrongCount !== 1 ? 's' : ''}
                    </span>
                    <span className="text-xs text-grey">
                      in {item.attempts.length} attempt{item.attempts.length !== 1 ? 's' : ''}
                    </span>
                  </div>

                  <h4 className="text-charcoal font-medium mb-3 leading-relaxed">
                    {item.question?.question}
                  </h4>

                  {item.question && (
                    <div className="space-y-2">
                      <p className="text-sm text-grey">Correct Answer:</p>
                      <div className="bg-accent/10 border border-accent/20 rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-accent text-sm">
                            {item.question.correct}:
                          </span>
                          <span className="text-charcoal text-sm">
                            {item.question.choices[
                              item.question.correct === "A" ? 0 :
                              item.question.correct === "B" ? 1 :
                              item.question.correct === "C" ? 2 : 3
                            ]}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}


