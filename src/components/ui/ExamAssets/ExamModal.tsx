"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { dtcAssets } from "@/lib/assets";
import { useUser } from "@/hooks/useUser";
import {
  type ExamMode,
  type ExamConfiguration,
  type AttemptSummary,
  getCurrentAttempt,
  getAttemptSummaries,
  createExamAttempt,
  abandonExamAttempt
} from "@/Services/examAttemptsService";
import { listQuestions, type QuestionRecord } from "@/Services/questionsService";
import { Clock, Trophy, Target, AlertTriangle, Play, Settings, History, X, CheckCircle2, BookOpen, BarChart3, ArrowRight } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";

interface ExamModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: ExamMode;
  certificateId: string;
  onStartExam: (attemptId: string) => void;
}

const modeConfig = {
  practice: {
    title: "Practice Mode",
    icon: Target,
    color: "text-accent",
    bgColor: "bg-accent/10",
  },
  realExam: {
    title: "Real Exam Mode",
    icon: Clock,
    color: "text-red-500",
    bgColor: "bg-red-50",
  },
  questionBank: {
    title: "Question Bank",
    icon: Trophy,
    color: "text-primary",
    bgColor: "bg-primary/10",
  },
};

export function ExamModal({ isOpen, onClose, mode, certificateId, onStartExam }: ExamModalProps) {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();
  const [loading, setLoading] = useState(false);
  const [currentAttempt, setCurrentAttempt] = useState<any>(null);
  const [previousAttempts, setPreviousAttempts] = useState<AttemptSummary[]>([]);
  const [availableQuestions, setAvailableQuestions] = useState<QuestionRecord[]>([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<"configure" | "history">("configure");

  // Configuration state
  const [configuration, setConfiguration] = useState<ExamConfiguration>({
    mode,
    numberOfQuestions: 10,
    selectedTopics: [],
    difficulty: "All",
    timerMinutes: 60,
  });

  const config = modeConfig[mode];
  const IconComponent = config.icon;

  // Helper functions
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (isOpen && user) {
      loadData();
    }
  }, [isOpen, user, certificateId]);

  const loadData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Load current attempt
      const current = await getCurrentAttempt(user.uid, certificateId);
      setCurrentAttempt(current);

      // Load previous attempts
      const summaries = await getAttemptSummaries(user.uid, certificateId);
      setPreviousAttempts(summaries.filter(s => s.status === "completed"));

      // Load available questions
      const questions = await listQuestions(certificateId);
      setAvailableQuestions(questions);

      // Extract unique topics
      const topics = [...new Set(questions.map(q => q.topic).filter(Boolean))];
      setAvailableTopics(topics);

      // Set default number of questions
      if (mode !== "questionBank") {
        setConfiguration(prev => ({
          ...prev,
          numberOfQuestions: Math.min(10, questions.length),
        }));
      }
    } catch (error) {
      console.error("Error loading exam data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartNewAttempt = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Filter questions based on configuration
      let filteredQuestions = availableQuestions;

      // Filter by topics
      if (configuration.selectedTopics && configuration.selectedTopics.length > 0) {
        filteredQuestions = filteredQuestions.filter(q => 
          configuration.selectedTopics!.includes(q.topic || "")
        );
      }

      // Filter by difficulty
      if (configuration.difficulty !== "All") {
        filteredQuestions = filteredQuestions.filter(q => q.difficulty === configuration.difficulty);
      }

      // Limit number of questions (except for question bank mode)
      if (mode !== "questionBank" && configuration.numberOfQuestions) {
        // Shuffle and take the specified number
        const shuffled = [...filteredQuestions].sort(() => Math.random() - 0.5);
        filteredQuestions = shuffled.slice(0, configuration.numberOfQuestions);
      }

      if (filteredQuestions.length === 0) {
        alert("No questions match your criteria. Please adjust your settings.");
        return;
      }

      const questionIds = filteredQuestions.map(q => q.id);
      const attemptId = await createExamAttempt(user.uid, certificateId, configuration, questionIds);

      router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}/exam/${attemptId}`);
      onClose();
    } catch (error) {
      console.error("Error creating exam attempt:", error);
      alert("Failed to start exam. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleContinueAttempt = () => {
    if (currentAttempt) {
      router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}/exam/${currentAttempt.id}`);
      onClose();
    }
  };

  const handleAbandonAttempt = async () => {
    if (!user || !currentAttempt) return;

    setLoading(true);
    try {
      await abandonExamAttempt(user.uid, currentAttempt.id);
      setCurrentAttempt(null);
    } catch (error) {
      console.error("Error abandoning attempt:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeSpent = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] p-0 bg-white shadow-2xl rounded-2xl overflow-hidden flex flex-col">
        <DialogTitle className="sr-only">
          {config.title} Configuration
        </DialogTitle>
        {/* Professional Header with DTC Logo */}
        <div className="bg-white border-b border-gray-100 px-8 py-6 relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-primary/5 rounded-lg p-3 border border-primary/10">
                <Image
                  src={dtcAssets.logoBlack}
                  alt="DTC Logo"
                  width={28}
                  height={28}
                  className="object-contain"
                />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-charcoal">{config.title}</h2>
                <p className="text-grey text-sm">Configure your examination parameters</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className={`p-2.5 rounded-lg ${config.bgColor} border border-primary/10`}>
                <IconComponent className={`h-5 w-5 ${config.color}`} />
              </div>
              <Button
                onClick={onClose}
                variant="ghost"
                size="icon"
                className="h-9 w-9 text-grey hover:text-charcoal hover:bg-gray-50 rounded-lg"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-100">
          <div className="flex px-8">
            <button
              onClick={() => setActiveTab("configure")}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === "configure"
                  ? "border-primary text-primary"
                  : "border-transparent text-grey hover:text-charcoal"
              }`}
            >
              Configure Exam
            </button>
            <button
              onClick={() => setActiveTab("history")}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === "history"
                  ? "border-primary text-primary"
                  : "border-transparent text-grey hover:text-charcoal"
              }`}
            >
              Previous Attempts ({previousAttempts.length})
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-8">{/* Content will go here */}

          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
                <p className="text-grey text-lg">Loading exam data...</p>
              </div>
            </div>
          ) : activeTab === "configure" ? (
            currentAttempt ? (
            // Current attempt in progress
            <div className="space-y-6">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-amber-100 rounded-lg p-2.5">
                    <AlertTriangle className="h-5 w-5 text-amber-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-charcoal mb-2">Examination in Progress</h3>
                    <p className="text-grey text-sm mb-4">
                      You have an active {config.title.toLowerCase()} session. You may continue from where you left off or abandon the current attempt to begin anew.
                    </p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-white border border-gray-100 rounded-lg p-3">
                        <p className="text-xs font-medium text-grey uppercase tracking-wide mb-1">Progress</p>
                        <p className="text-sm font-semibold text-charcoal">
                          {currentAttempt.answers?.length || 0} of {currentAttempt.totalQuestions} questions
                        </p>
                      </div>
                      <div className="bg-white border border-gray-100 rounded-lg p-3">
                        <p className="text-xs font-medium text-grey uppercase tracking-wide mb-1">Started</p>
                        <p className="text-sm font-semibold text-charcoal">{formatDate(currentAttempt.startedAt)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleContinueAttempt}
                  className="flex-1 bg-primary hover:bg-primary-deep text-white h-11 rounded-lg font-medium"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Continue Examination
                </Button>
                <Button
                  onClick={handleAbandonAttempt}
                  variant="outline"
                  className="h-11 px-6 rounded-lg font-medium border-gray-200 text-grey hover:bg-gray-50 hover:text-charcoal"
                  disabled={loading}
                >
                  Abandon & Start New
                </Button>
              </div>
            </div>
          ) : (
            // Configuration only
            <div className="space-y-8">
              {/* Configuration */}
              {mode !== "questionBank" ? (
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-gray-100 rounded-lg p-2.5">
                      <Settings className="h-5 w-5 text-grey" />
                    </div>
                    <h3 className="text-lg font-semibold text-charcoal">Examination Configuration</h3>
                  </div>
                  <div className="space-y-8">
                    {/* Number of Questions - Slider */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-charcoal">Number of Questions</label>
                        <span className="text-sm font-semibold text-primary bg-primary/10 px-3 py-1 rounded-md">
                          {configuration.numberOfQuestions || 10}
                        </span>
                      </div>
                      <div className="px-3">
                        <Slider
                          value={[configuration.numberOfQuestions || 10]}
                          onValueChange={(value) => setConfiguration(prev => ({
                            ...prev,
                            numberOfQuestions: value[0]
                          }))}
                          max={availableQuestions.length}
                          min={1}
                          step={1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-grey mt-2">
                          <span>1</span>
                          <span>{availableQuestions.length} available</span>
                        </div>
                      </div>
                    </div>

                    {/* Timer (Real Exam Mode only) */}
                    {mode === "realExam" && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <label className="text-sm font-medium text-charcoal">Timer Duration (minutes)</label>
                          <span className="text-sm font-semibold text-primary bg-primary/10 px-3 py-1 rounded-md">
                            {configuration.timerMinutes || 60}
                          </span>
                        </div>
                        <div className="px-3">
                          <Slider
                            value={[configuration.timerMinutes || 60]}
                            onValueChange={(value) => setConfiguration(prev => ({
                              ...prev,
                              timerMinutes: value[0]
                            }))}
                            max={180}
                            min={15}
                            step={15}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-grey mt-2">
                            <span>15 min</span>
                            <span>180 min</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Difficulty */}
                    <div className="space-y-4">
                      <label className="text-sm font-medium text-charcoal">Difficulty Level</label>
                      <div className="grid grid-cols-4 gap-2">
                        {["All", "Easy", "Medium", "Hard"].map((diff) => (
                          <button
                            key={diff}
                            onClick={() => setConfiguration(prev => ({ ...prev, difficulty: diff as any }))}
                            className={`h-10 rounded-lg text-sm font-medium transition-all ${
                              configuration.difficulty === diff
                                ? 'bg-primary text-white border border-primary'
                                : 'bg-white text-grey hover:bg-gray-50 hover:text-charcoal border border-gray-200'
                            }`}
                          >
                            {diff}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Topics */}
                    {availableTopics.length > 0 && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <label className="text-sm font-medium text-charcoal">Question Topics</label>
                          <span className="text-xs text-grey">
                            {configuration.selectedTopics?.length === 0 ? "All topics" : `${configuration.selectedTopics?.length} selected`}
                          </span>
                        </div>
                        <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                          {availableTopics.map((topic) => (
                            <label
                              key={topic}
                              className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer transition-colors"
                            >
                              <input
                                type="checkbox"
                                checked={configuration.selectedTopics?.includes(topic) || false}
                                onChange={(e) => {
                                  setConfiguration(prev => ({
                                    ...prev,
                                    selectedTopics: e.target.checked
                                      ? [...(prev.selectedTopics || []), topic]
                                      : (prev.selectedTopics || []).filter(t => t !== topic)
                                  }));
                                }}
                                className="w-4 h-4 text-primary bg-white border-gray-300 rounded focus:ring-primary focus:ring-2"
                              />
                              <span className="text-sm font-medium text-charcoal flex-1">{topic}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                // Question Bank Mode - Show information instead of configuration
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-gray-100 rounded-lg p-2.5">
                      <BookOpen className="h-5 w-5 text-grey" />
                    </div>
                    <h3 className="text-lg font-semibold text-charcoal">Question Bank Overview</h3>
                  </div>

                  <div className="bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg p-6 border border-primary/20">
                    <div className="text-center space-y-4">
                      <div className="bg-white rounded-lg p-4 inline-block">
                        <BarChart3 className="h-8 w-8 text-primary mx-auto mb-2" />
                        <p className="text-2xl font-bold text-charcoal">{availableQuestions.length}</p>
                        <p className="text-sm text-grey">Total Questions</p>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-charcoal mb-2">Complete Question Bank</h4>
                        <p className="text-sm text-grey mb-4">
                          Answer all questions from the beginning to the end of the question bank.
                          This mode provides comprehensive coverage of all available topics.
                        </p>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="bg-white rounded-lg p-3 border border-gray-100">
                            <p className="font-medium text-charcoal">No Time Limit</p>
                            <p className="text-grey">Take your time</p>
                          </div>
                          <div className="bg-white rounded-lg p-3 border border-gray-100">
                            <p className="font-medium text-charcoal">Immediate Feedback</p>
                            <p className="text-grey">Learn as you go</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {availableTopics.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-charcoal mb-3">Covered Topics</h4>
                      <div className="flex flex-wrap gap-2">
                        {availableTopics.map((topic) => (
                          <span
                            key={topic}
                            className="px-3 py-1.5 bg-gray-100 text-grey text-xs font-medium rounded-md"
                          >
                            {topic}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

            </div>
          )
          ) : (
            // Previous Attempts History Tab
            <div className="space-y-6">
              {previousAttempts.length === 0 ? (
                <div className="text-center py-16">
                  <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                    <History className="h-12 w-12 text-grey" />
                  </div>
                  <h3 className="text-xl font-semibold text-charcoal mb-2">No Previous Attempts</h3>
                  <p className="text-grey">You haven't taken this exam before. Start your first attempt!</p>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-charcoal">Previous Attempts</h3>
                    <span className="text-sm text-grey">{previousAttempts.length} attempt{previousAttempts.length !== 1 ? 's' : ''}</span>
                  </div>

                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {previousAttempts.map((attempt) => (
                      <div
                        key={attempt.id}
                        onClick={() => {
                          router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}/exam/${attempt.id}/report`);
                          onClose();
                        }}
                        className="bg-white border border-gray-200 rounded-lg p-6 hover:border-primary/30 hover:shadow-md transition-all cursor-pointer group"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-lg ${config.bgColor} border border-primary/10 group-hover:scale-110 transition-transform`}>
                              <IconComponent className={`h-5 w-5 ${config.color}`} />
                            </div>
                            <div>
                              <div className="flex items-center gap-3 mb-1">
                                <h4 className="font-semibold text-charcoal">Attempt #{attempt.id}</h4>
                                <div className={`px-2 py-1 rounded-md text-xs font-medium ${
                                  (attempt.score || 0) >= 70
                                    ? 'bg-green-100 text-green-700 border border-green-200'
                                    : 'bg-red-100 text-red-700 border border-red-200'
                                }`}>
                                  {(attempt.score || 0) >= 70 ? 'Passed' : 'Failed'}
                                </div>
                              </div>
                              <div className="flex items-center gap-4 text-sm text-grey">
                                <span>Score: <strong className="text-charcoal">{attempt.score || 0}%</strong></span>
                                <span>•</span>
                                <span>{attempt.correctAnswers || 0}/{attempt.totalQuestions || 0} correct</span>
                                <span>•</span>
                                <span>{formatDate(attempt.startedAt)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-3">
                            <div className="text-right">
                              <p className="text-2xl font-bold text-charcoal">{attempt.score || 0}%</p>
                              <p className="text-xs text-grey">
                                {attempt.timeSpentSeconds ? formatTimeSpent(attempt.timeSpentSeconds) : 'N/A'}
                              </p>
                            </div>
                            <div className="text-primary group-hover:translate-x-1 transition-transform">
                              <ArrowRight className="h-5 w-5" />
                            </div>
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="mt-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-500 ${
                                (attempt.score || 0) >= 70
                                  ? 'bg-gradient-to-r from-accent to-primary'
                                  : 'bg-gradient-to-r from-red-400 to-red-500'
                              }`}
                              style={{ width: `${attempt.score || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Fixed Footer with Start Button - only show in configure tab when not loading and no current attempt */}
        {activeTab === "configure" && !loading && !currentAttempt && (
          <div className="border-t border-gray-100 bg-white p-6">
            <div className="flex justify-end">
              <Button
                onClick={handleStartNewAttempt}
                disabled={loading}
                className="bg-primary hover:bg-primary-deep text-white px-8 h-12 rounded-lg font-medium text-base"
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/20 border-t-white"></div>
                    Starting...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Play className="h-5 w-5" />
                    Begin {mode === "questionBank" ? "Question Bank" : "Examination"}
                  </div>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
