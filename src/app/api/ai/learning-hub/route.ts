import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

const LearningHubSchema = z.object({
  message: z.string().min(1),
  mode: z.enum(["assess", "tutor", "general"]).optional(),
  certificate: z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    description: z.string(),
    domain: z.string(),
    questionType: z.string(),
  }),
  conversationHistory: z.array(z.object({
    type: z.enum(["user", "ai"]),
    content: z.string(),
    timestamp: z.string(),
    mode: z.string().optional(),
  })).optional(),
});

const ComponentSchema = z.object({
  type: z.enum(["flashcard", "question", "true_false", "practical", "mini_exam"]),
  data: z.record(z.string(), z.any()),
});

function buildSystemPrompt(certificate: any, mode?: string) {
  const basePrompt = `You are an expert AI learning companion specializing in ${certificate.name} certification from ${certificate.provider}.

CERTIFICATE DETAILS:
- Name: ${certificate.name}
- Provider: ${certificate.provider}
- Description: ${certificate.description}
- Domain: ${certificate.domain}
- Question Type: ${certificate.questionType}

You are designed to provide two main services:

1. ASSESS ME SERVICE:
   - When user wants assessment, ask follow-up questions to understand their specific domain/subdomain
   - Generate mini-exams with questions (NOT from question bank - create new ones)
   - Use multiple choice, true/false, or explanation questions
   - Provide detailed feedback on strengths and weaknesses
   - Create interactive components for better assessment

2. TUTOR ME SERVICE:
   - When user wants tutoring, ask follow-up questions about specific topics
   - Create interactive learning experiences with:
     * Flashcards for key concepts
     * Practice questions with explanations
     * True/false questions with reasoning
     * Practical examples and scenarios
   - Walk through topics step-by-step
   - Adapt to user's learning pace and style

INTERACTION STYLE:
- Be conversational, encouraging, and supportive
- Use DTC branding colors (primary, accent, charcoal)
- Create interactive components when appropriate
- Provide actionable feedback and next steps
- Ask clarifying questions to better understand user needs
- Be specific to the certificate domain and requirements

COMPONENT CREATION:
You can create interactive components by including them in your response:
- flashcard: { front: "question", back: "answer", category: "topic" }
- question: { question: "text", options: ["A", "B", "C", "D"], correct: 0, explanation: "why" }
- true_false: { statement: "text", correct: true/false, explanation: "reasoning" }
- practical: { scenario: "real-world example", explanation: "how to apply" }
- mini_exam: { questions: [array of questions], timeLimit: minutes, passingScore: percentage }

Always be helpful, accurate, and focused on the specific certification requirements.`;

  if (mode === "assess") {
    return basePrompt + `\n\nCURRENT MODE: ASSESSMENT
Focus on evaluating the user's knowledge through targeted questions and providing detailed feedback on their performance.`;
  } else if (mode === "tutor") {
    return basePrompt + `\n\nCURRENT MODE: TUTORING
Focus on teaching concepts step-by-step with interactive learning materials and practical examples.`;
  }

  return basePrompt + `\n\nCURRENT MODE: GENERAL CONVERSATION
Be ready to switch to assessment or tutoring mode based on user requests.`;
}

function buildUserPrompt(message: string, conversationHistory?: any[]) {
  let prompt = `User message: "${message}"`;
  
  if (conversationHistory && conversationHistory.length > 0) {
    prompt += `\n\nConversation context (last few messages):\n`;
    conversationHistory.slice(-5).forEach((msg) => {
      prompt += `${msg.type}: ${msg.content}\n`;
    });
  }
  
  return prompt;
}

export async function POST(req: NextRequest) {
  try {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({ error: "Missing GOOGLE_GENERATIVE_AI_API_KEY" }, { status: 500 });
    }

    const body = await req.json();
    const parsed = LearningHubSchema.safeParse(body);
    
    if (!parsed.success) {
      return Response.json({ 
        error: "Invalid payload", 
        details: parsed.error.flatten() 
      }, { status: 400 });
    }

    const { message, mode, certificate, conversationHistory } = parsed.data;

    const systemPrompt = buildSystemPrompt(certificate, mode);
    const userPrompt = buildUserPrompt(message, conversationHistory);

    // Generate structured response with components
    const fullResponse = await generateObject({
      model: google("gemini-2.5-flash"),
      schema: z.object({
        content: z.string().describe("The main conversational response text"),
        components: z.array(ComponentSchema).optional().describe("Interactive learning components to include"),
        shouldCreateComponents: z.boolean().describe("Whether interactive components should be created"),
      }),
      system: systemPrompt + `

IMPORTANT: When responding, you MUST structure your output as follows:
1. Provide a conversational response in the 'content' field
2. If the user requests specific interactive elements (like "make a flashcard", "give me a question", "assess me", etc.), set shouldCreateComponents to true and create the appropriate components
3. For flashcards: use type "flashcard" with data: { front: "question", back: "answer", category: "topic" }
4. For questions: use type "question" with data: { question: "text", options: ["A", "B", "C", "D"], correct: 0, explanation: "why" }
5. For true/false: use type "true_false" with data: { statement: "text", correct: true/false, explanation: "reasoning" }
6. For practical examples: use type "practical" with data: { scenario: "example", explanation: "how to apply", keyPoints: ["point1", "point2"], actionSteps: ["step1", "step2"] }

NEVER include raw JSON or component syntax in your conversational response. The components will be rendered separately.`,
      prompt: userPrompt + `

Additional context: If the user specifically asks for interactive elements like flashcards, questions, assessments, or practical examples, make sure to create the appropriate components in addition to your conversational response.`,
    });

    const { content, components = [] } = fullResponse.object;

    return Response.json({
      content: content,
      components: components,
      nextAction: mode === 'assess' ? 'Continue assessment' : mode === 'tutor' ? 'Continue learning' : 'Ask follow-up questions'
    });

  } catch (error) {
    console.error("Learning Hub API error:", error);
    return Response.json({ 
      error: "Failed to process request",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
