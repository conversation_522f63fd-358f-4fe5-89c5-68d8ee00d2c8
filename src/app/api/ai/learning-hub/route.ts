import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject, generateText } from "ai";
import { z } from "zod";
import { getCertificate } from "@/Services/certificateDetails";

const LearningHubSchema = z.object({
  certificateId: z.string(),
  message: z.string(),
  conversationHistory: z.array(z.object({
    role: z.enum(["user", "assistant"]),
    content: z.string(),
    timestamp: z.number(),
  })).optional(),
  mode: z.enum(["assess", "tutor", "general"]).optional(),
  currentStep: z.string().optional(),
});

const AssessmentResponseSchema = z.object({
  type: z.enum(["clarification", "assessment", "results"]),
  message: z.string(),
  questions: z.array(z.object({
    id: z.string(),
    question: z.string(),
    type: z.enum(["multiple_choice", "explanation"]),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().optional(),
  })).optional(),
  assessment: z.object({
    score: z.number(),
    strengths: z.array(z.string()),
    weaknesses: z.array(z.string()),
    recommendations: z.array(z.string()),
  }).optional(),
});

const TutorResponseSchema = z.object({
  type: z.enum(["clarification", "lesson", "flashcard", "question", "practical"]),
  message: z.string(),
  content: z.object({
    title: z.string().optional(),
    explanation: z.string().optional(),
    flashcard: z.object({
      front: z.string(),
      back: z.string(),
    }).optional(),
    question: z.object({
      question: z.string(),
      type: z.enum(["true_false", "multiple_choice", "explanation"]),
      options: z.array(z.string()).optional(),
      correctAnswer: z.string(),
      explanation: z.string(),
    }).optional(),
    practical: z.object({
      scenario: z.string(),
      steps: z.array(z.string()),
      keyPoints: z.array(z.string()),
    }).optional(),
  }).optional(),
});

export async function POST(req: NextRequest) {
  try {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return Response.json({ error: "Missing GOOGLE_GENERATIVE_AI_API_KEY" }, { status: 500 });
    }

    const body = await req.json();
    const parsed = LearningHubSchema.safeParse(body);
    if (!parsed.success) {
      return Response.json({ error: "Invalid payload", details: parsed.error.flatten() }, { status: 400 });
    }

    const { certificateId, message, conversationHistory = [], mode, currentStep } = parsed.data;

    // Get certificate details
    const certificate = await getCertificate(certificateId);
    if (!certificate) {
      return Response.json({ error: "Certificate not found" }, { status: 404 });
    }

    // Determine the mode if not specified
    const detectedMode = mode || detectMode(message);
    
    if (detectedMode === "assess") {
      return await handleAssessment(certificate, message, conversationHistory, currentStep);
    } else if (detectedMode === "tutor") {
      return await handleTutoring(certificate, message, conversationHistory, currentStep);
    } else {
      return await handleGeneral(certificate, message, conversationHistory);
    }

  } catch (e: any) {
    console.error("Learning Hub API Error:", e);
    return Response.json({ error: e?.message ?? "Server error" }, { status: 500 });
  }
}

function detectMode(message: string): "assess" | "tutor" | "general" {
  const assessKeywords = ["assess", "test", "evaluate", "check", "quiz", "exam", "how good am i"];
  const tutorKeywords = ["teach", "learn", "explain", "tutor", "help me understand", "walk me through"];
  
  const lowerMessage = message.toLowerCase();
  
  if (assessKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return "assess";
  }
  if (tutorKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return "tutor";
  }
  return "general";
}

async function handleAssessment(
  certificate: any,
  message: string,
  history: any[],
  currentStep?: string
) {
  const systemPrompt = `You are an expert assessment AI for the ${certificate.name} certification by ${certificate.provider}.

Certificate Description: ${certificate.description}
Domain: ${certificate.domain}

Your role is to assess the user's knowledge through intelligent questioning. Follow this process:

1. CLARIFICATION: First, ask follow-up questions to understand exactly what domain/subdomain they want to be assessed on
2. ASSESSMENT: Generate 3-5 targeted questions (mix of multiple choice and explanation questions)
3. RESULTS: Analyze their answers and provide detailed feedback with strengths, weaknesses, and recommendations

Be conversational, encouraging, and provide detailed explanations. Generate questions that test real understanding, not just memorization.

Current conversation context: ${history.map(h => `${h.role}: ${h.content}`).join('\n')}`;

  const result = await generateObject({
    model: google("gemini-2.5-flash"),
    schema: AssessmentResponseSchema,
    prompt: `${systemPrompt}\n\nUser message: ${message}\nCurrent step: ${currentStep || 'initial'}`,
  });

  return Response.json({ 
    mode: "assess", 
    response: result.object,
    usage: result.usage 
  });
}

async function handleTutoring(
  certificate: any,
  message: string,
  history: any[],
  currentStep?: string
) {
  const systemPrompt = `You are an expert tutor AI for the ${certificate.name} certification by ${certificate.provider}.

Certificate Description: ${certificate.description}
Domain: ${certificate.domain}

Your role is to be an interactive study partner. You can:
- Create flashcards for key concepts
- Generate practice questions (true/false, multiple choice, explanations)
- Provide practical scenarios and step-by-step walkthroughs
- Break down complex topics into digestible parts

Be engaging, use real-world examples, and adapt your teaching style to the user's responses.

Current conversation context: ${history.map(h => `${h.role}: ${h.content}`).join('\n')}`;

  const result = await generateObject({
    model: google("gemini-2.5-flash"),
    schema: TutorResponseSchema,
    prompt: `${systemPrompt}\n\nUser message: ${message}\nCurrent step: ${currentStep || 'initial'}`,
  });

  return Response.json({ 
    mode: "tutor", 
    response: result.object,
    usage: result.usage 
  });
}

async function handleGeneral(
  certificate: any,
  message: string,
  history: any[]
) {
  const systemPrompt = `You are an AI learning assistant for the ${certificate.name} certification by ${certificate.provider}.

Certificate Description: ${certificate.description}
Domain: ${certificate.domain}

You help users with general questions about the certification, study strategies, and can guide them to either:
1. Assessment mode - to test their knowledge
2. Tutoring mode - to learn specific topics

Be helpful, encouraging, and always relate back to the certification context.

Current conversation context: ${history.map(h => `${h.role}: ${h.content}`).join('\n')}`;

  const result = await generateText({
    model: google("gemini-2.5-flash"),
    prompt: `${systemPrompt}\n\nUser message: ${message}`,
  });

  return Response.json({ 
    mode: "general", 
    response: { 
      type: "general",
      message: result.text 
    },
    usage: result.usage 
  });
}
